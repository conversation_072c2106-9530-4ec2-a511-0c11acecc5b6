using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using ProjectDigitizer.Core.Models;
using ProjectDigitizer.Core.Enums;
using ProjectDigitizer.Application.Commands;

namespace ProjectDigitizer.Application.ViewModels;

/// <summary>
/// 模块节点的视图模型
/// </summary>
public class ModuleNodeViewModel : ViewModelBase
{
    private ModuleModel? _module;
    private bool _isSelected;
    private bool _isLightBulbOn = true;
    private bool _isExpanded;
    private bool _isLocked;
    private bool _isEnabled = true;
    private string _headerBackgroundHex = "#808080";
    private string? _title;
    private Point _location;
    private NodePropertyValues _propertyValues;

    public ModuleNodeViewModel()
    {
        Inputs = new ObservableCollection<ConnectorViewModel>();
        Outputs = new ObservableCollection<ConnectorViewModel>();
        Conditions = new ObservableCollection<string>();
        ExpandedContent = new ObservableCollection<object>();
        _propertyValues = new NodePropertyValues();

        // 初始化命令
        ToggleExpandCommand = new DelegateCommand(() => IsExpanded = !IsExpanded);

        // 默认设置位置
        Location = new Point(100, 100);

        // 默认标题
        Title = "未命名模块";

        // 配置连接器
        ConfigureConnectors();
    }

    #region Properties

    /// <summary>
    /// 对应的模块模型
    /// </summary>
    public ModuleModel? Module
    {
        get => _module;
        set
        {
            if (_module != value)
            {
                BatchUpdate(() =>
                {
                    _module = value;
                    OnPropertyChanged(nameof(Module));
                    OnPropertyChanged(nameof(IconGlyph));
                    OnPropertyChanged(nameof(StatusBrush));
                    OnPropertyChanged(nameof(NodeType));
                    OnPropertyChanged(nameof(NodeTypeMetadata));
                    UpdateHeaderColor();
                    ConfigureConnectors();
                    InitializeExpandedContent();
                    InitializeNodeProperties();
                    Title = _module?.Name;
                });
            }
        }
    }

    /// <summary>
    /// 节点位置
    /// </summary>
    public Point Location
    {
        get => _location;
        set => SetProperty(ref _location, value);
    }

    /// <summary>
    /// 节点标题
    /// </summary>
    public string? Title
    {
        get => _title;
        set => SetProperty(ref _title, value);
    }

    /// <summary>
    /// 是否选中
    /// </summary>
    public bool IsSelected
    {
        get => _isSelected;
        set => SetProperty(ref _isSelected, value);
    }

    /// <summary>
    /// 灯泡是否点亮
    /// </summary>
    public bool IsLightBulbOn
    {
        get => _isLightBulbOn;
        set => SetProperty(ref _isLightBulbOn, value);
    }

    /// <summary>
    /// 是否展开
    /// </summary>
    public bool IsExpanded
    {
        get => _isExpanded;
        set => SetProperty(ref _isExpanded, value);
    }

    /// <summary>
    /// 是否锁定
    /// </summary>
    public bool IsLocked
    {
        get => _isLocked;
        set => SetProperty(ref _isLocked, value);
    }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled
    {
        get => _isEnabled;
        set => SetProperty(ref _isEnabled, value);
    }

    /// <summary>
    /// 标题栏背景颜色（十六进制）
    /// </summary>
    public string HeaderBackgroundHex
    {
        get => _headerBackgroundHex;
        set => SetProperty(ref _headerBackgroundHex, value);
    }

    /// <summary>
    /// 输入连接器集合
    /// </summary>
    public ObservableCollection<ConnectorViewModel> Inputs { get; }

    /// <summary>
    /// 输出连接器集合
    /// </summary>
    public ObservableCollection<ConnectorViewModel> Outputs { get; }

    /// <summary>
    /// 条件集合
    /// </summary>
    public ObservableCollection<string> Conditions { get; }

    /// <summary>
    /// 展开内容集合
    /// </summary>
    public ObservableCollection<object> ExpandedContent { get; }

    /// <summary>
    /// 属性值容器
    /// </summary>
    public NodePropertyValues PropertyValues
    {
        get => _propertyValues;
        set => SetProperty(ref _propertyValues, value);
    }

    /// <summary>
    /// 图标字形
    /// </summary>
    public string IconGlyph
    {
        get
        {
            return Module?.Type switch
            {
                ModuleType.PipeLine => "\uE8B7",
                ModuleType.DataFilter => "\uE71C",
                ModuleType.ExcelExport => "\uE8C3",
                ModuleType.CADInput => "\uE8A5",
                _ => "\uE8F4"
            };
        }
    }

    /// <summary>
    /// 状态画刷颜色
    /// </summary>
    public string StatusBrush
    {
        get
        {
            if (!IsEnabled) return "#808080"; // 灰色 - 禁用
            if (!IsLightBulbOn) return "#FFA500"; // 橙色 - 警告
            return "#00FF00"; // 绿色 - 正常
        }
    }

    /// <summary>
    /// 节点类型
    /// </summary>
    public NodeType NodeType => Module?.NodeType ?? NodeType.Input;

    /// <summary>
    /// 节点类型元数据
    /// </summary>
    public NodeTypeMetadata NodeTypeMetadata
    {
        get
        {
            return new NodeTypeMetadata
            {
                NodeType = NodeType,
                Name = Module?.Name ?? "未知",
                Description = Module?.Description ?? "",
                IconGlyph = IconGlyph,
                ColorTheme = HeaderBackgroundHex
            };
        }
    }

    /// <summary>
    /// 输入标签
    /// </summary>
    public string InputLabel => "输入";

    /// <summary>
    /// 输出标签
    /// </summary>
    public string OutputLabel => "输出";

    #endregion

    #region Commands

    /// <summary>
    /// 切换展开命令
    /// </summary>
    public ICommand ToggleExpandCommand { get; }

    #endregion

    #region Public Methods

    /// <summary>
    /// 切换启用状态
    /// </summary>
    public void ToggleEnabled()
    {
        if (Module != null)
        {
            Module.IsEnabled = !Module.IsEnabled;
            IsEnabled = Module.IsEnabled;
        }
    }

    /// <summary>
    /// 切换灯泡状态
    /// </summary>
    public void ToggleLightBulb()
    {
        IsLightBulbOn = !IsLightBulbOn;
    }

    /// <summary>
    /// 切换锁定状态
    /// </summary>
    public void ToggleLocked()
    {
        IsLocked = !IsLocked;
    }

    #endregion

    #region Private Methods

    private void UpdateHeaderColor()
    {
        if (Module == null) return;

        HeaderBackgroundHex = Module.Type switch
        {
            ModuleType.PipeLine => "#2196F3", // 蓝色
            ModuleType.RiserPipe => "#4CAF50", // 绿色
            ModuleType.DataFilter => "#FF9800", // 橙色
            ModuleType.ExcelExport => "#9C27B0", // 紫色
            ModuleType.CADInput => "#F44336", // 红色
            _ => "#808080" // 灰色
        };
    }

    private void ConfigureConnectors()
    {
        Inputs.Clear();
        Outputs.Clear();

        if (Module == null) return;

        // 根据模块类型配置连接器
        switch (Module.NodeType)
        {
            case NodeType.Input:
                // 输入节点只有输出连接器
                Outputs.Add(new ConnectorViewModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Title = "输出",
                    IsInput = false,
                    DataType = "Data",
                    Owner = this
                });
                break;

            case NodeType.Transform:
                // 处理节点有输入和输出连接器
                Inputs.Add(new ConnectorViewModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Title = "输入",
                    IsInput = true,
                    DataType = "Data",
                    Owner = this
                });
                Outputs.Add(new ConnectorViewModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Title = "输出",
                    IsInput = false,
                    DataType = "Data",
                    Owner = this
                });
                break;

            case NodeType.Output:
                // 输出节点只有输入连接器
                Inputs.Add(new ConnectorViewModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Title = "输入",
                    IsInput = true,
                    DataType = "Data",
                    Owner = this
                });
                break;

            case NodeType.Control:
                // 控制节点可能有多个输入输出
                Inputs.Add(new ConnectorViewModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Title = "条件",
                    IsInput = true,
                    DataType = "Boolean",
                    Owner = this
                });
                Outputs.Add(new ConnectorViewModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Title = "真",
                    IsInput = false,
                    DataType = "Data",
                    Owner = this
                });
                Outputs.Add(new ConnectorViewModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Title = "假",
                    IsInput = false,
                    DataType = "Data",
                    Owner = this
                });
                break;
        }
    }

    private void InitializeExpandedContent()
    {
        ExpandedContent.Clear();
        
        if (Module == null) return;

        // 根据模块类型添加展开内容
        // 这里可以添加特定于模块类型的UI元素
    }

    private void InitializeNodeProperties()
    {
        if (Module == null) return;

        // 初始化节点属性的默认值
        // 这里可以根据模块类型设置默认属性
    }

    #endregion
}

1、数据导入节点(含筛选)
用途及释义:用于对工作流数据导入的关键节点。提供筛选逻辑和选结果列表。
①CAD 二次开发的 DB 类数据。
②excel、文件数据导入
③其他数据文件的导入
2、手动输入数据节点
用途及释义:直接手动输入的数据，其实也算一种函数节点。但是纯数据输入更需要明确数据输入的内容。作为数据输入的汇总节点也是有必要存在的。
三、函数节点(数据衍生节点)
1、一般函数节点
2、数据筛选节点(不知道有没有必要)
3、类表节点
用途及释义:用户展开数据组类型的数据，或者对现有数据通过表格的形式展现
1、数组展开节点
2、类 excel 表格节点
4、其他类型函数(以上三个无法表达的)
四、条件类节点
用途及释义:用于判断，数据在工作流层面整合判断等。有部分功能和函数节点重复。但是对一些逻辑分支，是有必要的。对数组进行分类、对
五、智能体节点
六、数据整理节点
七、数据导出节点(该节点执行需要确认)
1、导出 excel(材料表类类格式)、word、PPT 文件
2、导出 csv、txt 类型文件。
3、导出 CAD 格式文件:①导出材料表类内容，②部分大样图的粘贴
﻿using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ProjectDigitizer.Application.DependencyInjection;
using ProjectDigitizer.Core.Interfaces;

namespace ProjectDigitizer.Studio;

/// <summary>
/// 应用程序入口点
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        // 构建主机和依赖注入容器
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // 注册应用层服务
                services.AddApplicationServices();
                services.AddLoggingServices();

                // 注册WPF窗口
                services.AddTransient<MainWindow>();
            })
            .Build();

        // 启动主机
        _host.Start();

        // 获取主窗口并显示
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }
}

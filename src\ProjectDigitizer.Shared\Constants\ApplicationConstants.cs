namespace ProjectDigitizer.Shared.Constants;

/// <summary>
/// 应用程序常量
/// </summary>
public static class ApplicationConstants
{
    /// <summary>
    /// 应用程序名称
    /// </summary>
    public const string ApplicationName = "ProjectDigitizer";

    /// <summary>
    /// 应用程序版本
    /// </summary>
    public const string Version = "1.0.0";

    /// <summary>
    /// 配置文件名
    /// </summary>
    public const string ConfigFileName = "appsettings.json";

    /// <summary>
    /// 日志文件夹名
    /// </summary>
    public const string LogsFolderName = "Logs";

    /// <summary>
    /// 项目文件扩展名
    /// </summary>
    public const string ProjectFileExtension = ".pdproj";

    /// <summary>
    /// 模板文件扩展名
    /// </summary>
    public const string TemplateFileExtension = ".pdtemplate";
}

/// <summary>
/// 文件类型常量
/// </summary>
public static class FileTypeConstants
{
    public const string Excel = "Excel文件|*.xlsx;*.xls";
    public const string CSV = "CSV文件|*.csv";
    public const string JSON = "JSON文件|*.json";
    public const string XML = "XML文件|*.xml";
    public const string CAD = "CAD文件|*.dwg;*.dxf";
    public const string AllSupported = "所有支持的文件|*.xlsx;*.xls;*.csv;*.json;*.xml;*.dwg;*.dxf";
}

/// <summary>
/// UI常量
/// </summary>
public static class UIConstants
{
    /// <summary>
    /// 默认窗口宽度
    /// </summary>
    public const double DefaultWindowWidth = 1200;

    /// <summary>
    /// 默认窗口高度
    /// </summary>
    public const double DefaultWindowHeight = 800;

    /// <summary>
    /// 最小窗口宽度
    /// </summary>
    public const double MinWindowWidth = 800;

    /// <summary>
    /// 最小窗口高度
    /// </summary>
    public const double MinWindowHeight = 600;

    /// <summary>
    /// 默认画布缩放级别
    /// </summary>
    public const double DefaultZoomLevel = 1.0;

    /// <summary>
    /// 最小缩放级别
    /// </summary>
    public const double MinZoomLevel = 0.1;

    /// <summary>
    /// 最大缩放级别
    /// </summary>
    public const double MaxZoomLevel = 5.0;
}

﻿<Application x:Class="ProjectDigitizer.Studio.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:ProjectDigitizer.Studio"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Telerik Fluent主题资源 - 使用正确的路径 -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/System.Windows.xaml"/> -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/Telerik.Windows.Controls.xaml"/> -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/Telerik.Windows.Controls.Input.xaml"/> -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/Telerik.Windows.Controls.Navigation.xaml"/> -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/Telerik.Windows.Controls.RibbonView.xaml"/> -->
                <!-- <ResourceDictionary Source="/Telerik.Windows.Themes.Fluent;component/Themes/Telerik.Windows.Controls.RichTextBox.xaml"/> -->

                <!-- MaterialDesign 主题 -->
                <materialDesign:BundledTheme BaseTheme="Light"
                                             PrimaryColor="Blue"
                                             SecondaryColor="DeepPurple"/>

                <!-- Nodify 主题（使用浅色主题配合我们的 Fluent Design） -->
                <ResourceDictionary Source="pack://application:,,,/Nodify;component/Themes/Light.xaml"/>

                <!-- 我们的 Fluent Design 节点模板 -->
                <ResourceDictionary Source="Resources/NodeTemplates.xaml"/>

                <!-- 节点属性面板模板 -->
                <ResourceDictionary Source="Controls/NodePropertyPanels.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>

---
标题: "**数据处理端**"
创建日期: 2025-05-16 10:46:11
修改日期: 2025-05-16 10:46:47
---

`    `写在前面：因为本软件功能之间大部分是单向关联的，即使存在动态调整的关联，要么需要点击按钮执行、要么就是轻量的。因此模块化设计注意的就是：开发环境的统一、各个模块位于软件功能逻辑的位置、数据统一定义形式、UI 交互的习惯的习惯统一。

适当考虑 UI 颜色，整体界面可调（白色、黑色、灰色等），

适当考虑支持 多国语言的可能。

适当考虑 兼容 MCP 协议。

以上工作不一定本次需要做，能考虑就适当考虑。

画布端基本操作，小控件拖拽进画布，小控件的选中亮显、位置锁定、框选或者单独拖动、删除；连接线的断开和启动按钮，连接线的删除、连接的动态效果。

画布两个状态，编辑状态能对大部分控件进行修改等操作。启动状态能启动工作流、关闭其中的内容等。

控件每个画布最开始有启动按钮，代表了这个数据流的开始。

在工程设计领域，数据流界面除了开始和导出文件，数据筛选和数据转换生成可以看成一个数据处理模块。

几个模块可以用一个大的框来形成一个组，拖拽组可以带动组里面的控件移动。

# **数据处理端**

1. ## **名词解释**

   1. 窗体 用于窗口交互的界面统称为窗体，主要的初始界面称为主窗体
   1. 材料库（字典） 用于各种材料的列表，默认不可修改
   1. 画布 模板栏目里最大子模板排列组合的空间
   1. 模板 类似于图面数据和材料库（字典）对应关系的统称
   1. 子模板（子模块） 不同类型提供不同的模块框 填入数据是子模板概念
   1. 项目 指的是一个项目为单位的工程
   1. 子项目 项目下面分的子项目 一个项目最多三个层级 即 1→1.1→1.1.1
   1. 项目信息 指的是项目确定之后保存在 CAD 图元中、或者传递到数据处理端的关于项目的名称、时间、创建者、子项目、编号等信息。
   1. 智能文档 所有的文档是由各种章节排列而成，由于工程文档不需要太多格式，所以根据文档具体表现内容进行章节定制。然后进行排列组合形成智能文档概念。
   1. 域 和 word 相似但是是基于一些信息源进行外部关联的称呼。

2. ## **界面样式**

   2\.1 界面样式功能叙述

   2\.1.1 标题栏选项卡 位于主窗体最上方，自定义的项目的称呼的信息，未定义则显示"新项目"

   2\.1.2 菜单栏 "文件"、"模板"、"数据"、"材料表"、"智能文本（说明书）"、"规范与图库"、材料库（字典）"、"工具"、"帮助"

   `      `"文件"点击会出现下拉："新建项目"、"保存项目"、"另存为项目"；

   "模板"点击会出现下拉："导入本地模板"、"导入云端模板"、"存为本地模板"、"存为云端模板"；

   "数据"点击会出现下拉："CAD 数据导入"、"数据库数据导入"、"表格数据导入"、"项目信息"；

   "材料表"点击会出现下拉："导出准备"（可导出材料表样式）、"生成材料表（导出内容排列与组合）"、"导出到 CAD"、"导出为 excel 文件"、"另存为项目"、

   "智能文本（说明书）" 类似于通过提取该项目内容生成文本来成果，

   "规范与图库" 点击会进入规范、图集、vip 图库索引界面。这是一个使用者的数据库，自带目录树索引 pdf 规范文件。提供关键字搜索。提供大样图、图集等文件。支持一件导入到指定 CAD。

   "材料库（字典）"点击会出现下拉："云端更新材料库"（默认自动更新）、"本地上传材料库"、"清空本地材料库"、"自定义时间与版本"（关于版本与时间的输出问题）；

   "知识库"、

   "工具"、"导入批注"

   "GYS" 本条不在本次范围内。提供造价软件接入。

   2\.1.3 功能区

   位于菜单栏下方，提供一些比较常用的操作交互内容，包含一些菜单里面的设置。

   2\.1.4 选项卡区

   "模板"、"说明"、"项目信息"、"材料偏好"、"材料表"、等等

   2\.2 界面样式

   ![](IMG-20250516104656638.png)

   以上"项目选项卡"下方展示的仅为模板空间，当点击其他，则会显示其他空间内容。下方可能根据需要增加状态栏等功能。

   2\.3"模板"

- 最左侧是子模板选择框，有几种类型（不同类型可以用不同颜色的子模板样式），（1）**常规数据类**：① 平面管线类，② 立管类，③ 调压箱调压柜类 ③ 开挖回填 ④ 破除恢复 ⑤ 防腐 ⑥ 防雷防静电（2）**数据衍生关联类** ① 警示带示踪线 ② 焊口探伤 ③ 安装台班 ④ 措施等。这方面可以继续思考下。
- 中间是"画布"从左侧子模板区域拖拽需要的子模板进画布，刚开始子模板的标题栏是灰色的。需要勾选生效。

根据需要

- 模板选项卡可以创建多个模板匹配不同的要求。如：模板一，模板二。。。模板，每个模板可以输入备注信息来区分模板适用内容。
- 子模板上方是标题栏，

  标题栏上面"复选框"、"灯泡按钮"、"清空"、"复制模板（复制到所有的模板）"、"删除"（可以右键删除，这个删除按钮不留了）、"一键导入"

  复选框中勾选不勾选对应启用或者不启动本条模板，第二个是一个灯泡，没点亮的的内容仅仅显示但是不会参与任何数据关联。在整个界面菜单栏可以隐藏没点亮的模板。让界面清晰。默认模板导入，是不勾选，点亮的状态。

  ![descript](IMG-20250516104656949.png)

- 每个子模板第一行有一个条件条目，用来选择数据来源及数据范围。点击可以添加条件选择条目。然后有一键导入。导入符合条件的数据。（筛选多个条件条目逻辑的或、且选择如何确定后面商议。）
- 整个画布界面功能区有：全部取消启用、全部启用、清空所有子模板、删除所有子模板、隐藏所有关闭的模板、删除所有关闭的模板、删除所有常规数据模板、删除所有数据衍生模板、复制当前选择的子模板到哪个模板。

2\.4 "项目信息"

项目信息窗体基本页面应该包含在 CAD 端和数据处理端共用的窗体。

"项目信息"菜单里有：

2\.4.1 项目编号、项目名称、创建时间、阶段、创建人等。

2\.4.2 子项目名称、子项名称编号。能进行编辑、排序、人工编号、自动编号、时间线等。

2\.4.3 根据现有绘图的内容自动生成基本信息，用以简单了解已经绘制的内容，辅助做出一些决策。可以是，小区名称管道长度，用户数量、小区数量、调压箱数量、以及一些其他信息。这些信息类似于一个**"智能文档"概念，**只是把这个项目概况索引功能提到此处来展示项目的基本信息。同时智能文档章节第一个章节也会显示该内容，但是是默认不参与文档生成的，只是便于操作者了解项目概况。

2\.5"材料偏好"

2\.5.1 材料表按照项目信息进行排序 增加可以增加一些来源不同的模板的数据此项内容原封照抄 2.4.2 的基本信息，并且能数据保持一致。同时增加一个图纸外可以增加的无法用图纸来呈现的子项目可能，同时，可以对材料的合并展示方式进行调整。

2\.5.2 材料表按照喜好对排列顺序进行排列

①"输出条目偏好"材料表名称对照表、材料表备注对照表。②"合并及顺序偏好"室内室外？ 埋地架空？ 中压低压？ 设备材料？根据材料发生的位置提供一个排列组合的内容。偏好可以设置多个 如：偏好一，偏好二。。。

2\.6"智能文档（说明书）"

类似一种文本性质的处理内容，支持部分富文本内容如：平方、立方、加粗、下划线、章节、表格的内容。其中可以增加一种词语的强关联功能，类似于 word 的域。

导出到 CAD 文件或者 word 文档。

在左侧应该是一种类似于 word 章节的那么一个内容。

左下方是类似于域的关联的索引

类似于 word 的域的内容包含规范内容、**规范时间**，项目**简介、范围**的关联**。**时间线和项目具体信息的更新。

3. ## **项目的基本数据导入**

   3\.1 CAD 连接通道数据导入

   点击菜单-CAD 数据导入 弹出数据选择窗体（类似于**项目信息**导入向导），窗体左侧是项目名称及子项名称的项目数，带复选框。可以选择哪些项目内容导入（初步想的是信息全部导入，但是提供项目中部分内容的导入的功能，也还可以？）。

   3\.2 手动输入

   3\.3

   3\.2.1 CAD 数据手工导入，这一项和 3.1 作用是一致的，所以在实现软件层面自动导入之前，可以手工输入此项数据。

4. ## **模板搭建设置导出**

   4\.1 模板导入

   4\.1.1 支持不同地域、不同公司、不同使用习惯或者私有模板的导入。点击菜单-模板导入，选择模板云模板还是本地模板-选择子模板（子模块）内容。

   4\.1.2 模板启用

   任何导入的模板是灰色的，需要在已经放置在画布中子模板（子模块）里点击

   4\.1.3 手动创建模板

   提供在项目界面设置集中类型的子模板的选择，双击或者拖动可以进入画布内。

   4\.1.4 模板导出

   模板存储的内容包括，① 模板（子模块）的使用情况和模板之间、② 材料库（字典）的数据关联关系，③ 生成的材料偏好 ④ 当前使用材料库字典的系统时间戳

   4\.1.5 后缀

   wft workfollow template 不带信息的模板文件

   wftd workfollow template & document 带模板和文档的工程文件

   gedb gas engineering databass 燃气信息数据

   gedic gas engineering dictionary 燃气工程字典文件

5. ## **主要数据的内容**

   5\.1 主要数据内容

   1. 基于 CAD 二次开发手动绘制的图纸带的项目内容数据。
   2. 基于输入的项目信息
   3. 操作人员额外补充的项目内容数据（CAD 数据之外的）
   4. 智能文档的内容信息
   5. 材料表的信息
   6. 材料库信息
   7. 模板信息：① 当前"画布"模板信息、② 当前材料表偏好信息（两种，一种是数据合并信息，另一种是偏好信息）③ 当前智能文档的的模板

   5\.3 数据处理端数据导入流程

   当绘制好图纸之后，在数据处理端点击导入 CAD 信息，此时为了创建模板进行关联试算，当模板建立好之后每个子项目都会关联属于哪个模板。

6. ## **材料表生成**

   ① 设置材料偏好 ② 生成材料 ③ 设置那些材料表对应那些偏好输出，那些单独输出，哪些合并输出。④ 根据材料表的内容生成到 CAD 文件。根据材料表导出 excel 文件。

   其中无论是材料表生成还是说明书生成都会有一个向导，比如字体字高，图纸大小等。才能根据相应设置进行图纸分幅。

7. ## **数据关联（域关联显示效果、数据启用的显示效果）**

   在模板里、在 word 里，对数据来源形式进行区分，对文本框进行填色处理。方便使用者做初步判断。

   7\.1 此概念适用范围，包含"画布"模板选项卡、智能文档、衍生与之相关的数据交互的窗体的内容。

   7\.2 数据标记形式设定：

| 序号 | 数据类型（来源）                                 | 颜色样式 | 备注            |
| ---- | ------------------------------------------------ | -------- | --------------- |
| 1    | 和项目信息关联的                                 | 紫色     | 底色            |
| 2    | 和 CAD 信息关联的                                | 橙色     | 底色            |
| 3    | 自己手动输入的参数                               | 绿色     | 底色            |
| 4    | 固定的默认的不可修改的                           | 灰色     | 底色            |
| 5    | 公式关联的结果                                   | 橙色     | 底色            |
| 6    | 材料库（字典）                                   | 蓝色     | 底色            |
| 7    | 规范与图库                                       | 青色     | 底色            |
| 8    | 特殊关联                                         | 紫色     | 底色            |
| 9    | 错误信息                                         | 红色     | 框 +闪烁        |
| 10   | 不启用的文档章节/不启用的子模板                  | 灰色     | 删除线/多选框？ |
| 11   | 不需要参与任何内容但是不想删除的文档章节和字幕版 | 淡灰色   | 淡显/灯泡？     |

新的思路，利用 ui 在每个数据框前面增加图标的形式进行标记。替代上面底色填充方式。具体内容可能需要优化，但是逻辑还是这样。

注：启用不启用的显示效果还没想好，但是基本功能就是这些。

8. ## **广告**
   制定一些广告组件，定期进行更新。

using System.Windows;

namespace ProjectDigitizer.Application.ViewModels;

/// <summary>
/// 连接器视图模型
/// </summary>
public class ConnectorViewModel : ViewModelBase
{
    private string _id = string.Empty;
    private string? _title;
    private bool _isInput;
    private string _dataType = "Data";
    private bool _isConnected;
    private Point _anchor;
    private ModuleNodeViewModel? _owner;

    public ConnectorViewModel()
    {
        _id = Guid.NewGuid().ToString();
    }

    #region Properties

    /// <summary>
    /// 连接器唯一标识
    /// </summary>
    public string Id
    {
        get => _id;
        set => SetProperty(ref _id, value);
    }

    /// <summary>
    /// 连接器标题
    /// </summary>
    public string? Title
    {
        get => _title;
        set => SetProperty(ref _title, value);
    }

    /// <summary>
    /// 是否为输入连接器
    /// </summary>
    public bool IsInput
    {
        get => _isInput;
        set => SetProperty(ref _isInput, value);
    }

    /// <summary>
    /// 是否为输出连接器
    /// </summary>
    public bool IsOutput => !IsInput;

    /// <summary>
    /// 数据类型
    /// </summary>
    public string DataType
    {
        get => _dataType;
        set => SetProperty(ref _dataType, value);
    }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected
    {
        get => _isConnected;
        set => SetProperty(ref _isConnected, value);
    }

    /// <summary>
    /// 连接器锚点位置
    /// </summary>
    public Point Anchor
    {
        get => _anchor;
        set => SetProperty(ref _anchor, value);
    }

    /// <summary>
    /// 所属的节点
    /// </summary>
    public ModuleNodeViewModel? Owner
    {
        get => _owner;
        set => SetProperty(ref _owner, value);
    }

    /// <summary>
    /// 连接器颜色（基于数据类型）
    /// </summary>
    public string ConnectorColor
    {
        get
        {
            return DataType switch
            {
                "Data" => "#2196F3",      // 蓝色 - 数据
                "Boolean" => "#4CAF50",   // 绿色 - 布尔
                "Number" => "#FF9800",    // 橙色 - 数字
                "String" => "#9C27B0",    // 紫色 - 字符串
                "File" => "#F44336",      // 红色 - 文件
                _ => "#808080"            // 灰色 - 未知
            };
        }
    }

    /// <summary>
    /// 连接器形状（基于输入/输出）
    /// </summary>
    public string ConnectorShape => IsInput ? "Circle" : "Square";

    #endregion

    #region Public Methods

    /// <summary>
    /// 检查是否可以连接到目标连接器
    /// </summary>
    /// <param name="target">目标连接器</param>
    /// <returns>是否可以连接</returns>
    public bool CanConnectTo(ConnectorViewModel target)
    {
        if (target == null) return false;
        if (target == this) return false; // 不能连接自己
        if (target.Owner == Owner) return false; // 不能连接同一个节点
        if (IsInput == target.IsInput) return false; // 输入只能连接输出

        // 检查数据类型兼容性
        return IsDataTypeCompatible(DataType, target.DataType);
    }

    /// <summary>
    /// 更新连接状态
    /// </summary>
    /// <param name="connected">是否连接</param>
    public void UpdateConnectionStatus(bool connected)
    {
        IsConnected = connected;
    }

    #endregion

    #region Private Methods

    private bool IsDataTypeCompatible(string sourceType, string targetType)
    {
        // 数据类型兼容性检查
        if (sourceType == targetType) return true;
        if (sourceType == "Data" || targetType == "Data") return true; // Data类型兼容所有类型
        
        // 可以添加更多的兼容性规则
        return false;
    }

    #endregion

    #region Equality

    public override bool Equals(object? obj)
    {
        if (obj is ConnectorViewModel other)
        {
            return Id == other.Id;
        }
        return false;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    #endregion
}

using System;
using System.Collections.Generic;
using System.Linq;
using ProjectDigitizer.Studio.ViewModels;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 连接线样式同步服务 - 管理连接线与字段引用状态的同步
    /// </summary>
    public class ConnectionStyleSyncService
    {
        private readonly Dictionary<string, ConnectionViewModel> _connections; // 连接ID -> 连接对象
        private readonly Dictionary<string, List<string>> _fieldConnections; // 字段ID -> 连接ID列表
        private FieldReferenceTracker? _referenceTracker;

        /// <summary>连接线样式更新事件</summary>
        public event EventHandler<ConnectionStyleChangedEventArgs>? ConnectionStyleChanged;

        public ConnectionStyleSyncService()
        {
            _connections = new Dictionary<string, ConnectionViewModel>();
            _fieldConnections = new Dictionary<string, List<string>>();
        }

        /// <summary>
        /// 设置字段引用追踪器
        /// </summary>
        /// <param name="referenceTracker">字段引用追踪器</param>
        public void SetReferenceTracker(FieldReferenceTracker referenceTracker)
        {
            // 取消之前的事件订阅
            if (_referenceTracker != null)
            {
                _referenceTracker.FieldReferenceChanged -= OnFieldReferenceChanged;
                _referenceTracker.ConnectionStateChanged -= OnConnectionStateChanged;
            }

            _referenceTracker = referenceTracker;

            // 订阅新的事件
            if (_referenceTracker != null)
            {
                _referenceTracker.FieldReferenceChanged += OnFieldReferenceChanged;
                _referenceTracker.ConnectionStateChanged += OnConnectionStateChanged;
            }
        }

        /// <summary>
        /// 注册连接线
        /// </summary>
        /// <param name="connection">连接线对象</param>
        /// <param name="fieldId">关联的字段ID</param>
        public void RegisterConnection(ConnectionViewModel connection, string fieldId)
        {
            if (connection == null || string.IsNullOrEmpty(fieldId))
                return;

            // 注册连接
            _connections[connection.Id] = connection;

            // 建立字段与连接的映射
            if (!_fieldConnections.ContainsKey(fieldId))
            {
                _fieldConnections[fieldId] = new List<string>();
            }

            if (!_fieldConnections[fieldId].Contains(connection.Id))
            {
                _fieldConnections[fieldId].Add(connection.Id);
            }

            // 通知引用追踪器连接状态
            _referenceTracker?.NotifyConnectionStateChanged(fieldId, true, connection);

            // 初始化连接线样式
            UpdateConnectionStyle(connection, fieldId);
        }

        /// <summary>
        /// 注销连接线
        /// </summary>
        /// <param name="connectionId">连接线ID</param>
        /// <param name="fieldId">关联的字段ID</param>
        public void UnregisterConnection(string connectionId, string fieldId)
        {
            if (string.IsNullOrEmpty(connectionId) || string.IsNullOrEmpty(fieldId))
                return;

            // 移除连接
            _connections.Remove(connectionId);

            // 移除字段映射
            if (_fieldConnections.ContainsKey(fieldId))
            {
                _fieldConnections[fieldId].Remove(connectionId);

                // 如果字段没有连接了，通知引用追踪器
                if (!_fieldConnections[fieldId].Any())
                {
                    _fieldConnections.Remove(fieldId);
                    _referenceTracker?.NotifyConnectionStateChanged(fieldId, false);
                }
            }
        }

        /// <summary>
        /// 批量注册连接线
        /// </summary>
        /// <param name="connections">连接线与字段ID的映射</param>
        public void RegisterConnections(Dictionary<ConnectionViewModel, string> connections)
        {
            foreach (var kvp in connections)
            {
                RegisterConnection(kvp.Key, kvp.Value);
            }
        }

        /// <summary>
        /// 更新所有连接线样式
        /// </summary>
        public void UpdateAllConnectionStyles()
        {
            foreach (var kvp in _fieldConnections)
            {
                var fieldId = kvp.Key;
                var connectionIds = kvp.Value;

                foreach (var connectionId in connectionIds)
                {
                    if (_connections.TryGetValue(connectionId, out var connection))
                    {
                        UpdateConnectionStyle(connection, fieldId);
                    }
                }
            }
        }

        /// <summary>
        /// 获取字段的连接线列表
        /// </summary>
        /// <param name="fieldId">字段ID</param>
        /// <returns>连接线列表</returns>
        public List<ConnectionViewModel> GetFieldConnections(string fieldId)
        {
            if (!_fieldConnections.ContainsKey(fieldId))
                return new List<ConnectionViewModel>();

            var connections = new List<ConnectionViewModel>();
            foreach (var connectionId in _fieldConnections[fieldId])
            {
                if (_connections.TryGetValue(connectionId, out var connection))
                {
                    connections.Add(connection);
                }
            }

            return connections;
        }

        /// <summary>
        /// 清空所有连接
        /// </summary>
        public void Clear()
        {
            _connections.Clear();
            _fieldConnections.Clear();
        }

        /// <summary>
        /// 字段引用变化处理
        /// </summary>
        private void OnFieldReferenceChanged(object? sender, FieldReferenceChangedEventArgs e)
        {
            UpdateFieldConnectionStyles(e.FieldId);
        }

        /// <summary>
        /// 连接状态变化处理
        /// </summary>
        private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
        {
            UpdateFieldConnectionStyles(e.FieldId);
        }

        /// <summary>
        /// 更新字段相关的所有连接线样式
        /// </summary>
        /// <param name="fieldId">字段ID</param>
        private void UpdateFieldConnectionStyles(string fieldId)
        {
            if (!_fieldConnections.ContainsKey(fieldId))
                return;

            foreach (var connectionId in _fieldConnections[fieldId])
            {
                if (_connections.TryGetValue(connectionId, out var connection))
                {
                    UpdateConnectionStyle(connection, fieldId);
                }
            }
        }

        /// <summary>
        /// 更新单个连接线样式
        /// </summary>
        /// <param name="connection">连接线对象</param>
        /// <param name="fieldId">字段ID</param>
        private void UpdateConnectionStyle(ConnectionViewModel connection, string fieldId)
        {
            if (_referenceTracker == null)
                return;

            var isReferenced = _referenceTracker.IsFieldReferenced(fieldId);
            var oldValue = connection.IsFieldReferenced;

            // 更新连接线的引用状态
            connection.IsFieldReferenced = isReferenced;

            // 更新流动动画状态
            UpdateFlowAnimationState(connection, isReferenced);

            // 如果状态发生变化，触发事件
            if (oldValue != isReferenced)
            {
                ConnectionStyleChanged?.Invoke(this, new ConnectionStyleChangedEventArgs(
                    connection, fieldId, isReferenced));

                System.Diagnostics.Debug.WriteLine(
                    $"连接线样式更新: 字段 {fieldId}, 引用状态 {isReferenced}, 流动状态 {connection.FlowState}");
            }
        }

        /// <summary>
        /// 根据字段名称查找字段ID
        /// </summary>
        /// <param name="fieldName">字段名称</param>
        /// <returns>字段ID（如果找到）</returns>
        public string? FindFieldIdByName(string fieldName)
        {
            // 简单实现：假设字段名称就是字段ID
            // 在实际应用中，可能需要更复杂的映射逻辑
            return _fieldConnections.Keys.FirstOrDefault(id =>
                id.Equals(fieldName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取连接线统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public ConnectionStatistics GetStatistics()
        {
            var totalConnections = _connections.Count;
            var referencedConnections = _connections.Values.Count(c => c.IsFieldReferenced);
            var enabledConnections = _connections.Values.Count(c => c.IsEnabled);

            return new ConnectionStatistics
            {
                TotalConnections = totalConnections,
                ReferencedConnections = referencedConnections,
                UnreferencedConnections = totalConnections - referencedConnections,
                EnabledConnections = enabledConnections,
                DisabledConnections = totalConnections - enabledConnections
            };
        }

        /// <summary>
        /// 更新连接线的流动动画状态
        /// </summary>
        /// <param name="connection">连接线对象</param>
        /// <param name="isReferenced">是否被引用</param>
        private void UpdateFlowAnimationState(ConnectionViewModel connection, bool isReferenced)
        {
            if (!connection.IsEnabled)
            {
                // 禁用的连接线不显示动画
                connection.IsFlowAnimationEnabled = false;
                connection.FlowState = ConnectionFlowState.None;
                return;
            }

            // 启用流动动画
            connection.IsFlowAnimationEnabled = true;

            // 根据引用状态设置流动效果
            if (isReferenced)
            {
                // 被引用的字段使用正常流动
                connection.FlowState = ConnectionFlowState.Normal;
                System.Diagnostics.Debug.WriteLine($"设置连接线 {connection.Id} 为正常流动状态");
            }
            else
            {
                // 未被引用的字段使用慢速流动
                connection.FlowState = ConnectionFlowState.Slow;
                System.Diagnostics.Debug.WriteLine($"设置连接线 {connection.Id} 为慢速流动状态");
            }

            System.Diagnostics.Debug.WriteLine($"连接线 {connection.Id} 流动动画已启用: {connection.IsFlowAnimationEnabled}, 状态: {connection.FlowState}");
        }

        /// <summary>
        /// 全局启用/禁用流动动画
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetGlobalFlowAnimationEnabled(bool enabled)
        {
            foreach (var connection in _connections.Values)
            {
                if (enabled && connection.IsEnabled)
                {
                    UpdateFlowAnimationState(connection, connection.IsFieldReferenced);
                }
                else
                {
                    connection.IsFlowAnimationEnabled = false;
                    connection.FlowState = ConnectionFlowState.None;
                }
            }
        }
    }

    /// <summary>
    /// 连接线样式变化事件参数
    /// </summary>
    public class ConnectionStyleChangedEventArgs : EventArgs
    {
        public ConnectionViewModel Connection { get; }
        public string FieldId { get; }
        public bool IsReferenced { get; }

        public ConnectionStyleChangedEventArgs(ConnectionViewModel connection, string fieldId, bool isReferenced)
        {
            Connection = connection;
            FieldId = fieldId;
            IsReferenced = isReferenced;
        }
    }

    /// <summary>
    /// 连接线统计信息
    /// </summary>
    public class ConnectionStatistics
    {
        public int TotalConnections { get; set; }
        public int ReferencedConnections { get; set; }
        public int UnreferencedConnections { get; set; }
        public int EnabledConnections { get; set; }
        public int DisabledConnections { get; set; }
    }
}

using System;
using System.Collections.Generic;
using System.ComponentModel;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Interfaces
{
    /// <summary>
    /// 节点基础接口
    /// </summary>
    public interface INode : INotifyPropertyChanged
    {
        /// <summary>
        /// 节点唯一标识
        /// </summary>
        string Id { get; set; }

        /// <summary>
        /// 节点名称
        /// </summary>
        string Name { get; set; }

        /// <summary>
        /// 节点描述
        /// </summary>
        string Description { get; set; }

        /// <summary>
        /// 模块类型
        /// </summary>
        ModuleType ModuleType { get; set; }

        /// <summary>
        /// 节点类型分类
        /// </summary>
        NodeType NodeType { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        bool IsEnabled { get; set; }

        /// <summary>
        /// 是否可见
        /// </summary>
        bool IsVisible { get; set; }

        /// <summary>
        /// 节点属性
        /// </summary>
        INodeProperties Properties { get; set; }

        /// <summary>
        /// 代码模板
        /// </summary>
        string CodeTemplate { get; set; }

        /// <summary>
        /// 验证节点配置
        /// </summary>
        /// <returns>验证结果</returns>
        ValidationResult Validate();

        /// <summary>
        /// 获取节点的输入端口定义
        /// </summary>
        /// <returns>输入端口列表</returns>
        IEnumerable<IPortDefinition> GetInputPorts();

        /// <summary>
        /// 获取节点的输出端口定义
        /// </summary>
        /// <returns>输出端口列表</returns>
        IEnumerable<IPortDefinition> GetOutputPorts();

        /// <summary>
        /// 克隆节点
        /// </summary>
        /// <returns>节点副本</returns>
        INode Clone();
    }

    /// <summary>
    /// 端口定义接口
    /// </summary>
    public interface IPortDefinition
    {
        /// <summary>
        /// 端口标识
        /// </summary>
        string Id { get; set; }

        /// <summary>
        /// 端口名称
        /// </summary>
        string Name { get; set; }

        /// <summary>
        /// 端口描述
        /// </summary>
        string Description { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        string DataType { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        bool IsRequired { get; set; }

        /// <summary>
        /// 是否允许多连接
        /// </summary>
        bool AllowMultipleConnections { get; set; }

        /// <summary>
        /// 端口位置（相对于节点）
        /// </summary>
        PortPosition Position { get; set; }
    }

    /// <summary>
    /// 端口位置枚举
    /// </summary>
    public enum PortPosition
    {
        Left,
        Right,
        Top,
        Bottom
    }

    /// <summary>
    /// 节点属性基础接口
    /// </summary>
    public interface INodeProperties : INotifyPropertyChanged
    {
        /// <summary>
        /// 获取属性值
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <returns>属性值</returns>
        object? GetValue(string propertyName);

        /// <summary>
        /// 获取强类型属性值
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="propertyName">属性名称</param>
        /// <returns>属性值</returns>
        T? GetValue<T>(string propertyName);

        /// <summary>
        /// 设置属性值
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="value">属性值</param>
        void SetValue(string propertyName, object? value);

        /// <summary>
        /// 获取所有属性值
        /// </summary>
        /// <returns>属性字典</returns>
        Dictionary<string, object?> GetAllValues();

        /// <summary>
        /// 验证属性
        /// </summary>
        /// <returns>验证结果</returns>
        ValidationResult ValidateProperties();

        /// <summary>
        /// 重置为默认值
        /// </summary>
        void ResetToDefaults();
    }

    /// <summary>
    /// 节点处理器接口
    /// </summary>
    public interface INodeProcessor
    {
        /// <summary>
        /// 支持的节点类型
        /// </summary>
        ModuleType SupportedModuleType { get; }

        /// <summary>
        /// 执行节点处理逻辑
        /// </summary>
        /// <param name="node">节点实例</param>
        /// <param name="inputData">输入数据</param>
        /// <returns>处理结果</returns>
        NodeExecutionResult Execute(INode node, Dictionary<string, object> inputData);

        /// <summary>
        /// 获取输入端口定义
        /// </summary>
        /// <returns>输入端口列表</returns>
        IEnumerable<IPortDefinition> GetInputPorts();

        /// <summary>
        /// 获取输出端口定义
        /// </summary>
        /// <returns>输出端口列表</returns>
        IEnumerable<IPortDefinition> GetOutputPorts();

        /// <summary>
        /// 验证节点配置
        /// </summary>
        /// <param name="node">节点实例</param>
        /// <returns>验证结果</returns>
        ValidationResult ValidateConfiguration(INode node);
    }

    /// <summary>
    /// 节点执行结果
    /// </summary>
    public class NodeExecutionResult
    {
        public bool IsSuccess { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> OutputData { get; set; } = new();
        public TimeSpan ExecutionTime { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 节点工厂接口
    /// </summary>
    public interface INodeFactory
    {
        /// <summary>
        /// 支持的模块类型
        /// </summary>
        IEnumerable<ModuleType> SupportedModuleTypes { get; }

        /// <summary>
        /// 创建节点实例
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <returns>节点实例</returns>
        INode CreateNode(ModuleType moduleType);

        /// <summary>
        /// 创建节点处理器
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <returns>节点处理器</returns>
        INodeProcessor CreateProcessor(ModuleType moduleType);

        /// <summary>
        /// 获取节点模板
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <returns>节点模板</returns>
        INodeTemplate GetNodeTemplate(ModuleType moduleType);
    }

    /// <summary>
    /// 节点模板接口
    /// </summary>
    public interface INodeTemplate
    {
        /// <summary>
        /// 模块类型
        /// </summary>
        ModuleType ModuleType { get; }

        /// <summary>
        /// 模板名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 模板描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 图标字形
        /// </summary>
        string IconGlyph { get; }

        /// <summary>
        /// 默认属性架构
        /// </summary>
        NodePropertySchema PropertySchema { get; }

        /// <summary>
        /// 默认代码模板
        /// </summary>
        string DefaultCodeTemplate { get; }

        /// <summary>
        /// 创建节点实例
        /// </summary>
        /// <returns>节点实例</returns>
        INode CreateInstance();
    }
}

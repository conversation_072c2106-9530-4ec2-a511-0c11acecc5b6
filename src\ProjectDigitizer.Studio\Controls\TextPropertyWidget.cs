using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 文本属性编辑器
    /// </summary>
    public class TextPropertyWidget : IPropertyWidget
    {
        private readonly TextBox _textBox;
        private PropertyDefinition _propertyDefinition = new();
        private object? _value;

        public TextPropertyWidget()
        {
            _textBox = new TextBox
            {
                Margin = new Thickness(0, 8, 0, 16),
                FontSize = 14,
                Padding = new Thickness(8),
                BorderThickness = new Thickness(1),
                BorderBrush = System.Windows.Media.Brushes.LightGray
            };

            // 安全地尝试应用MaterialDesign样式
            try
            {
                if (Application.Current.Resources.Contains("MaterialDesignFloatingHintTextBox"))
                {
                    _textBox.Style = Application.Current.Resources["MaterialDesignFloatingHintTextBox"] as Style;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to apply MaterialDesign style: {ex.Message}");
            }

            _textBox.TextChanged += OnTextChanged;
        }

        public PropertyDefinition PropertyDefinition
        {
            get => _propertyDefinition;
            set
            {
                _propertyDefinition = value;
                UpdateUI();
            }
        }

        public object? Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    var oldValue = _value;
                    _value = value;
                    _textBox.Text = value?.ToString() ?? string.Empty;
                    ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                        PropertyDefinition.Name, oldValue, value));
                }
            }
        }

        public bool IsEnabled
        {
            get => _textBox.IsEnabled;
            set => _textBox.IsEnabled = value;
        }

        public event EventHandler<PropertyValueChangedEventArgs>? ValueChanged;

        public Models.ValidationResult Validate()
        {
            var result = new Models.ValidationResult();
            var text = _textBox.Text;

            // 必填验证
            if (PropertyDefinition.Required && string.IsNullOrWhiteSpace(text))
            {
                result.AddError($"{PropertyDefinition.Title} 是必填项");
                return result;
            }

            // 正则表达式验证
            foreach (var rule in PropertyDefinition.ValidationRules)
            {
                if (rule.Type == "pattern" && !string.IsNullOrEmpty(rule.Pattern))
                {
                    if (!Regex.IsMatch(text, rule.Pattern))
                    {
                        result.AddError(rule.ErrorMessage);
                        return result;
                    }
                }
            }

            return result;
        }

        public FrameworkElement GetElement()
        {
            return _textBox;
        }

        private void UpdateUI()
        {
            _textBox.Text = PropertyDefinition.DefaultValue?.ToString() ?? string.Empty;

            // 安全地设置MaterialDesign的浮动提示
            try
            {
                if (!string.IsNullOrEmpty(PropertyDefinition.Title))
                {
                    _textBox.SetValue(MaterialDesignThemes.Wpf.HintAssist.HintProperty, PropertyDefinition.Title);
                }

                // 设置帮助文本
                if (!string.IsNullOrEmpty(PropertyDefinition.Description))
                {
                    _textBox.SetValue(MaterialDesignThemes.Wpf.HintAssist.HelperTextProperty, PropertyDefinition.Description);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to set MaterialDesign properties: {ex.Message}");
                // 回退到基本的ToolTip
                if (!string.IsNullOrEmpty(PropertyDefinition.Description))
                {
                    _textBox.ToolTip = PropertyDefinition.Description;
                }
            }
        }

        private void OnTextChanged(object sender, TextChangedEventArgs e)
        {
            var oldValue = _value;
            _value = _textBox.Text;
            ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                PropertyDefinition.Name, oldValue, _value));
        }
    }
}
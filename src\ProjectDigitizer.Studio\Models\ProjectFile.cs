using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProjectDigitizer.Studio.Models
{
    /// <summary>
    /// 项目文件数据结构
    /// </summary>
    public class ProjectFile : INotifyPropertyChanged
    {
        private ProjectInfo _projectInfo = new();
        private CanvasData _canvasData = new();
        private List<TemplateData> _templates = new();
        private string _version = "1.0";
        private DateTime _createdTime = DateTime.Now;
        private DateTime _lastModifiedTime = DateTime.Now;

        /// <summary>
        /// 项目信息
        /// </summary>
        public ProjectInfo ProjectInfo
        {
            get => _projectInfo;
            set => SetProperty(ref _projectInfo, value);
        }

        /// <summary>
        /// 画布数据
        /// </summary>
        public CanvasData CanvasData
        {
            get => _canvasData;
            set => SetProperty(ref _canvasData, value);
        }

        /// <summary>
        /// 模板列表
        /// </summary>
        public List<TemplateData> Templates
        {
            get => _templates;
            set => SetProperty(ref _templates, value);
        }

        /// <summary>
        /// 文件版本
        /// </summary>
        public string Version
        {
            get => _version;
            set => SetProperty(ref _version, value);
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime
        {
            get => _createdTime;
            set => SetProperty(ref _createdTime, value);
        }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedTime
        {
            get => _lastModifiedTime;
            set => SetProperty(ref _lastModifiedTime, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// 项目基本信息
    /// </summary>
    public class ProjectInfo
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "新项目";
        public string Description { get; set; } = "";
        public string Creator { get; set; } = Environment.UserName;
        public DateTime CreateTime { get; set; } = DateTime.Now;
        public string Phase { get; set; } = "设计阶段";
        public string ProjectNumber { get; set; } = "";
        public List<SubProject> SubProjects { get; set; } = new();
        
        /// <summary>
        /// 项目统计信息
        /// </summary>
        public ProjectStatistics Statistics { get; set; } = new();
    }

    /// <summary>
    /// 子项目信息
    /// </summary>
    public class SubProject
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string Number { get; set; } = "";
        public int Order { get; set; } = 0;
        public string ParentId { get; set; } = "";
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 项目统计信息
    /// </summary>
    public class ProjectStatistics
    {
        public double TotalPipelineLength { get; set; } = 0;
        public int UserCount { get; set; } = 0;
        public int CommunityCount { get; set; } = 0;
        public int PressureBoxCount { get; set; } = 0;
        public Dictionary<string, object> CustomStatistics { get; set; } = new();
    }

    /// <summary>
    /// 画布数据
    /// </summary>
    public class CanvasData
    {
        public List<NodeData> Nodes { get; set; } = new();
        public List<ConnectionData> Connections { get; set; } = new();
        public CanvasSettings Settings { get; set; } = new();
        public string CurrentTemplate { get; set; } = "默认模板";
    }

    /// <summary>
    /// 节点数据
    /// </summary>
    public class NodeData
    {
        public string Id { get; set; } = "";
        public string Title { get; set; } = "";
        public ModuleType Type { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
        public bool IsEnabled { get; set; } = true;
        public bool IsLightBulbOn { get; set; } = true;
        public bool IsExpanded { get; set; } = false;
        public Dictionary<string, object?> PropertyValues { get; set; } = new();
        public List<string> Conditions { get; set; } = new();
        public List<ConnectorData> Inputs { get; set; } = new();
        public List<ConnectorData> Outputs { get; set; } = new();
    }

    /// <summary>
    /// 连接器数据
    /// </summary>
    public class ConnectorData
    {
        public string Id { get; set; } = "";
        public string Title { get; set; } = "";
        public string DataType { get; set; } = "";
        public bool IsInput { get; set; }
    }

    /// <summary>
    /// 连接线数据
    /// </summary>
    public class ConnectionData
    {
        public string Id { get; set; } = "";
        public string SourceNodeId { get; set; } = "";
        public string SourceConnectorId { get; set; } = "";
        public string TargetNodeId { get; set; } = "";
        public string TargetConnectorId { get; set; } = "";
        public bool IsEnabled { get; set; } = true;
    }

    /// <summary>
    /// 画布设置
    /// </summary>
    public class CanvasSettings
    {
        public double ZoomLevel { get; set; } = 1.0;
        public double OffsetX { get; set; } = 0;
        public double OffsetY { get; set; } = 0;
        public bool ShowGrid { get; set; } = true;
        public bool SnapToGrid { get; set; } = true;
        public double GridSize { get; set; } = 20;
        public Dictionary<string, object> CustomSettings { get; set; } = new();
    }

    /// <summary>
    /// 模板数据
    /// </summary>
    public class TemplateData
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Category { get; set; } = "";
        public DateTime CreatedTime { get; set; } = DateTime.Now;
        public string Creator { get; set; } = "";
        public List<NodeData> Nodes { get; set; } = new();
        public List<ConnectionData> Connections { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}

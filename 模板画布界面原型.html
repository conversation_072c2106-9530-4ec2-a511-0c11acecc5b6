<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工程项目数字化解决软件 - 模板画布界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }

        body {
            background-color: #f5f5f5;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
        }

        /* 顶部菜单栏 */
        .top-menu {
            background-color: #333;
            color: white;
            padding: 5px 10px;
            display: flex;
            align-items: center;
        }

        .top-menu .logo {
            font-weight: bold;
            margin-right: 20px;
        }

        .top-menu .menu-item {
            margin: 0 10px;
            cursor: pointer;
        }

        /* 功能区 */
        .function-area {
            background-color: #e0e0e0;
            padding: 5px;
            display: flex;
            flex-wrap: wrap;
            border-bottom: 1px solid #ccc;
        }

        .function-button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 5px 10px;
            margin: 2px 5px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .function-button:hover {
            background-color: #3367d6;
        }

        /* 选项卡区 */
        .tab-area {
            background-color: #f0f0f0;
            display: flex;
            border-bottom: 1px solid #ccc;
        }

        .tab {
            padding: 8px 15px;
            cursor: pointer;
            border-right: 1px solid #ccc;
        }

        .tab.active {
            background-color: white;
            border-bottom: 2px solid #4285f4;
        }

        /* 主内容区 */
        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        /* 左侧子模板选择区 */
        .template-selector {
            width: 250px;
            background-color: #f0f0f0;
            border-right: 1px solid #ccc;
            overflow-y: auto;
            padding: 10px;
        }

        .template-category {
            margin-bottom: 15px;
        }

        .category-title {
            font-weight: bold;
            padding: 5px;
            background-color: #e0e0e0;
            border-radius: 3px;
            margin-bottom: 5px;
            cursor: pointer;
        }

        .template-item {
            padding: 8px;
            margin: 5px 0;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 3px;
            cursor: move;
            transition: all 0.2s;
        }

        .template-item.data-type {
            border-left: 4px solid #4285f4;
        }

        .template-item.relation-type {
            border-left: 4px solid #0f9d58;
        }

        .template-item:hover {
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        /* 画布区域 */
        .canvas-area {
            flex: 1;
            background-color: white;
            background-image:
                linear-gradient(#e0e0e0 1px, transparent 1px),
                linear-gradient(90deg, #e0e0e0 1px, transparent 1px);
            background-size: 20px 20px;
            position: relative;
            overflow: auto;
        }

        /* 画布中的模板 */
        .canvas-template {
            position: absolute;
            width: 250px;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 3px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .template-header {
            background-color: #e0e0e0;
            padding: 8px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #ccc;
        }

        .template-header.active {
            background-color: #4285f4;
            color: white;
        }

        .template-header.inactive {
            background-color: #9e9e9e;
            color: #f5f5f5;
        }

        .template-title {
            flex: 1;
            font-weight: bold;
        }

        .template-controls {
            display: flex;
        }

        .template-control {
            margin-left: 5px;
            cursor: pointer;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .template-body {
            padding: 10px;
        }

        .condition-row {
            padding: 5px;
            border: 1px dashed #ccc;
            margin-bottom: 10px;
            background-color: #f9f9f9;
            cursor: pointer;
        }

        .condition-item {
            padding: 5px;
            margin: 5px 0;
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 3px;
            display: flex;
            align-items: center;
        }

        .condition-item select {
            margin: 0 5px;
            padding: 2px;
        }

        .condition-item input {
            margin: 0 5px;
            padding: 2px;
            border: 1px solid #ccc;
            border-radius: 2px;
        }

        .condition-item .remove-condition {
            margin-left: auto;
            cursor: pointer;
            color: #f44336;
            font-weight: bold;
        }

        .data-field {
            padding: 5px;
            margin: 5px 0;
            border-radius: 3px;
        }

        .data-field.project-info {
            background-color: #f3e5f5;
        }

        .data-field.cad-info {
            background-color: #fff3e0;
        }

        .data-field.manual-input {
            background-color: #e8f5e9;
        }

        .data-field.fixed-param {
            background-color: #eeeeee;
        }

        /* 连接线 */
        .connection-point {
            position: absolute;
            width: 12px;
            height: 12px;
            background-color: #4285f4;
            border-radius: 50%;
            cursor: pointer;
            z-index: 10;
        }

        .connection-point.input {
            top: -6px;
            left: 50%;
            transform: translateX(-50%);
        }

        .connection-point.output {
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
        }

        .connection-line {
            position: absolute;
            height: 2px;
            background-color: #4285f4;
            transform-origin: 0 0;
            z-index: 5;
            pointer-events: none;
        }

        /* 状态栏 */
        .status-bar {
            background-color: #f0f0f0;
            padding: 5px 10px;
            border-top: 1px solid #ccc;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部菜单栏 -->
        <div class="top-menu">
            <div class="logo">工程项目数字化解决软件</div>
            <div class="menu-item">文件</div>
            <div class="menu-item">模板</div>
            <div class="menu-item">数据</div>
            <div class="menu-item">材料表</div>
            <div class="menu-item">智能文本</div>
            <div class="menu-item">规范与图库</div>
            <div class="menu-item">材料库</div>
            <div class="menu-item">工具</div>
            <div class="menu-item">帮助</div>
        </div>

        <!-- 功能区 -->
        <div class="function-area">
            <button class="function-button">全部启用</button>
            <button class="function-button">全部取消启用</button>
            <button class="function-button">清空所有子模板</button>
            <button class="function-button">删除所有子模板</button>
            <button class="function-button">隐藏关闭的模板</button>
            <button class="function-button">删除关闭的模板</button>
            <button class="function-button">删除常规数据模板</button>
            <button class="function-button">删除数据衍生模板</button>
        </div>

        <!-- 选项卡区 -->
        <div class="tab-area">
            <div class="tab active">模板</div>
            <div class="tab">说明</div>
            <div class="tab">项目信息</div>
            <div class="tab">材料偏好</div>
            <div class="tab">材料表</div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 左侧子模板选择区 -->
            <div class="template-selector">
                <div class="template-category">
                    <div class="category-title">常规数据类</div>
                    <div class="template-item data-type">平面管线类</div>
                    <div class="template-item data-type">立管类</div>
                    <div class="template-item data-type">调压箱调压柜类</div>
                    <div class="template-item data-type">开挖回填</div>
                    <div class="template-item data-type">破除恢复</div>
                    <div class="template-item data-type">防腐</div>
                    <div class="template-item data-type">防雷防静电</div>
                </div>

                <div class="template-category">
                    <div class="category-title">数据衍生关联类</div>
                    <div class="template-item relation-type">警示带示踪线</div>
                    <div class="template-item relation-type">焊口探伤</div>
                    <div class="template-item relation-type">安装台班</div>
                    <div class="template-item relation-type">措施</div>
                </div>
            </div>

            <!-- 画布区域 -->
            <div class="canvas-area">
                <!-- 示例：画布中的模板 -->
                <div class="canvas-template" style="top: 50px; left: 300px;">
                    <div class="template-header active">
                        <input type="checkbox" checked>
                        <div class="template-title">平面管线类</div>
                        <div class="template-controls">
                            <div class="template-control">💡</div>
                            <div class="template-control">🗑️</div>
                            <div class="template-control">📋</div>
                        </div>
                    </div>
                    <div class="template-body">
                        <div class="condition-row">+ 点击添加条件条目</div>
                        <div class="data-field project-info">项目名称: 示例项目</div>
                        <div class="data-field cad-info">管线长度: 120m</div>
                        <div class="data-field manual-input">施工单位: [点击输入]</div>
                        <div class="data-field fixed-param">管材标准: GB50028</div>
                    </div>
                </div>

                <div class="canvas-template" style="top: 150px; left: 600px;">
                    <div class="template-header inactive">
                        <input type="checkbox">
                        <div class="template-title">焊口探伤</div>
                        <div class="template-controls">
                            <div class="template-control">💡</div>
                            <div class="template-control">🗑️</div>
                            <div class="template-control">📋</div>
                        </div>
                    </div>
                    <div class="template-body">
                        <div class="condition-row">+ 点击添加条件条目</div>
                        <div class="data-field cad-info">焊口数量: 24</div>
                        <div class="data-field manual-input">探伤比例: [点击输入]</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            就绪 | 模板数: 2 | 已启用: 1
        </div>
    </div>

    <script>
        // 拖拽功能实现
        document.addEventListener('DOMContentLoaded', function() {
            // 获取所有可拖拽的模板项
            const templateItems = document.querySelectorAll('.template-item');
            const canvasArea = document.querySelector('.canvas-area');

            // 为每个模板项添加拖拽事件
            templateItems.forEach(item => {
                item.setAttribute('draggable', 'true');

                item.addEventListener('dragstart', function(e) {
                    e.dataTransfer.setData('text/plain', item.textContent);
                    e.dataTransfer.setData('template-type', item.classList.contains('data-type') ? 'data-type' : 'relation-type');
                });
            });

            // 画布区域接收拖拽
            canvasArea.addEventListener('dragover', function(e) {
                e.preventDefault(); // 允许放置
            });

            canvasArea.addEventListener('drop', function(e) {
                e.preventDefault();

                // 获取拖拽的数据
                const templateName = e.dataTransfer.getData('text/plain');
                const templateType = e.dataTransfer.getData('template-type');

                // 创建新的模板元素
                createNewTemplate(templateName, templateType, e.clientX, e.clientY);
            });

            // 创建新模板函数
            function createNewTemplate(name, type, x, y) {
                // 创建模板容器
                const template = document.createElement('div');
                template.className = 'canvas-template';

                // 计算相对于画布的位置
                const canvasRect = canvasArea.getBoundingClientRect();
                const left = x - canvasRect.left;
                const top = y - canvasRect.top;

                template.style.left = left + 'px';
                template.style.top = top + 'px';

                // 模板头部
                const header = document.createElement('div');
                header.className = 'template-header inactive';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';

                const title = document.createElement('div');
                title.className = 'template-title';
                title.textContent = name;

                const controls = document.createElement('div');
                controls.className = 'template-controls';

                const lightControl = document.createElement('div');
                lightControl.className = 'template-control';
                lightControl.textContent = '💡';

                const deleteControl = document.createElement('div');
                deleteControl.className = 'template-control';
                deleteControl.textContent = '🗑️';

                const copyControl = document.createElement('div');
                copyControl.className = 'template-control';
                copyControl.textContent = '📋';

                controls.appendChild(lightControl);
                controls.appendChild(deleteControl);
                controls.appendChild(copyControl);

                header.appendChild(checkbox);
                header.appendChild(title);
                header.appendChild(controls);

                // 模板内容
                const body = document.createElement('div');
                body.className = 'template-body';

                const conditionRow = document.createElement('div');
                conditionRow.className = 'condition-row';
                conditionRow.textContent = '+ 点击添加条件条目';

                body.appendChild(conditionRow);

                // 添加一些默认数据字段
                if (type === 'data-type') {
                    const field1 = document.createElement('div');
                    field1.className = 'data-field project-info';
                    field1.textContent = '项目名称: 新项目';
                    body.appendChild(field1);

                    const field2 = document.createElement('div');
                    field2.className = 'data-field cad-info';
                    field2.textContent = '数据项: [自动获取]';
                    body.appendChild(field2);
                } else {
                    const field1 = document.createElement('div');
                    field1.className = 'data-field manual-input';
                    field1.textContent = '参数: [点击输入]';
                    body.appendChild(field1);
                }

                // 添加连接点
                const inputPoint = document.createElement('div');
                inputPoint.className = 'connection-point input';
                inputPoint.setAttribute('data-template-id', Date.now()); // 使用时间戳作为唯一ID

                const outputPoint = document.createElement('div');
                outputPoint.className = 'connection-point output';
                outputPoint.setAttribute('data-template-id', Date.now()); // 使用时间戳作为唯一ID

                // 组装模板
                template.appendChild(inputPoint);
                template.appendChild(outputPoint);
                template.appendChild(header);
                template.appendChild(body);

                // 添加到画布
                canvasArea.appendChild(template);

                // 添加复选框事件
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        header.className = 'template-header active';
                    } else {
                        header.className = 'template-header inactive';
                    }
                    updateStatusBar();
                });

                // 添加删除按钮事件
                deleteControl.addEventListener('click', function() {
                    template.remove();
                    updateStatusBar();
                });

                // 添加条件条目点击事件
                const conditionRowElement = template.querySelector('.condition-row');
                conditionRowElement.addEventListener('click', function() {
                    addConditionItem(this.parentNode);
                });

                // 添加模板拖动功能
                makeTemplateDraggable(template);

                // 添加连接点事件
                setupConnectionPoints(inputPoint, outputPoint);

                // 更新状态栏
                updateStatusBar();
            }

            // 使画布中的模板可拖动
            function makeTemplateDraggable(template) {
                const header = template.querySelector('.template-header');

                header.addEventListener('mousedown', function(e) {
                    if (e.target.classList.contains('template-control') || e.target.type === 'checkbox') {
                        return; // 如果点击的是控件，不启动拖动
                    }

                    const startX = e.clientX;
                    const startY = e.clientY;

                    const startLeft = parseInt(template.style.left) || 0;
                    const startTop = parseInt(template.style.top) || 0;

                    function mouseMoveHandler(e) {
                        const dx = e.clientX - startX;
                        const dy = e.clientY - startY;

                        template.style.left = (startLeft + dx) + 'px';
                        template.style.top = (startTop + dy) + 'px';
                    }

                    function mouseUpHandler() {
                        document.removeEventListener('mousemove', mouseMoveHandler);
                        document.removeEventListener('mouseup', mouseUpHandler);
                    }

                    document.addEventListener('mousemove', mouseMoveHandler);
                    document.addEventListener('mouseup', mouseUpHandler);
                });
            }

            // 为现有的模板添加拖动功能
            document.querySelectorAll('.canvas-template').forEach(template => {
                makeTemplateDraggable(template);

                // 添加复选框事件
                const checkbox = template.querySelector('input[type="checkbox"]');
                const header = template.querySelector('.template-header');

                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        header.className = 'template-header active';
                    } else {
                        header.className = 'template-header inactive';
                    }
                    updateStatusBar();
                });

                // 添加删除按钮事件
                const deleteControl = template.querySelector('.template-control:nth-child(2)');
                deleteControl.addEventListener('click', function() {
                    template.remove();
                    updateStatusBar();
                });

                // 为现有模板添加条件条目点击事件
                const conditionRow = template.querySelector('.condition-row');
                if (conditionRow) {
                    conditionRow.addEventListener('click', function() {
                        addConditionItem(this.parentNode);
                    });
                }
            });

            // 添加条件项函数
            function addConditionItem(parentElement) {
                const conditionItem = document.createElement('div');
                conditionItem.className = 'condition-item';

                // 创建数据源选择下拉框
                const sourceSelect = document.createElement('select');
                const sources = ['项目信息', 'CAD数据', '手动输入', '材料库'];
                sources.forEach(source => {
                    const option = document.createElement('option');
                    option.value = source;
                    option.textContent = source;
                    sourceSelect.appendChild(option);
                });

                // 创建条件类型选择下拉框
                const conditionSelect = document.createElement('select');
                const conditions = ['等于', '不等于', '大于', '小于', '包含', '不包含'];
                conditions.forEach(condition => {
                    const option = document.createElement('option');
                    option.value = condition;
                    option.textContent = condition;
                    conditionSelect.appendChild(option);
                });

                // 创建值输入框
                const valueInput = document.createElement('input');
                valueInput.type = 'text';
                valueInput.placeholder = '输入值';

                // 创建删除按钮
                const removeButton = document.createElement('span');
                removeButton.className = 'remove-condition';
                removeButton.textContent = '×';
                removeButton.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡
                    conditionItem.remove();
                });

                // 组装条件项
                conditionItem.appendChild(document.createTextNode('数据源: '));
                conditionItem.appendChild(sourceSelect);
                conditionItem.appendChild(document.createTextNode('条件: '));
                conditionItem.appendChild(conditionSelect);
                conditionItem.appendChild(document.createTextNode('值: '));
                conditionItem.appendChild(valueInput);
                conditionItem.appendChild(removeButton);

                // 插入到条件行之后
                const conditionRow = parentElement.querySelector('.condition-row');
                conditionRow.after(conditionItem);
            }

            // 更新状态栏信息
            function updateStatusBar() {
                const statusBar = document.querySelector('.status-bar');
                const templates = document.querySelectorAll('.canvas-template');
                const activeTemplates = document.querySelectorAll('.template-header.active');

                statusBar.textContent = `就绪 | 模板数: ${templates.length} | 已启用: ${activeTemplates.length}`;
            }

            // 初始化状态栏
            updateStatusBar();

            // 连接线功能
            let startPoint = null;
            let connections = [];

            // 为现有模板添加连接点
            document.querySelectorAll('.canvas-template').forEach((template, index) => {
                const templateId = 'template-' + index;

                // 添加输入连接点
                const inputPoint = document.createElement('div');
                inputPoint.className = 'connection-point input';
                inputPoint.setAttribute('data-template-id', templateId);

                // 添加输出连接点
                const outputPoint = document.createElement('div');
                outputPoint.className = 'connection-point output';
                outputPoint.setAttribute('data-template-id', templateId);

                template.appendChild(inputPoint);
                template.appendChild(outputPoint);

                // 添加连接点事件
                setupConnectionPoints(inputPoint, outputPoint);
            });

            // 设置连接点事件
            function setupConnectionPoints(inputPoint, outputPoint) {
                // 输出点点击事件
                outputPoint.addEventListener('click', function() {
                    if (startPoint === null) {
                        // 开始连接
                        startPoint = this;
                        this.style.backgroundColor = '#f44336'; // 变红表示选中
                    } else if (startPoint !== this) {
                        // 不能连接两个输出点
                        if (startPoint.classList.contains('output') && this.classList.contains('output')) {
                            alert('不能连接两个输出点');
                            startPoint.style.backgroundColor = '#4285f4';
                            startPoint = null;
                            return;
                        }

                        // 不能连接两个输入点
                        if (startPoint.classList.contains('input') && this.classList.contains('input')) {
                            alert('不能连接两个输入点');
                            startPoint.style.backgroundColor = '#4285f4';
                            startPoint = null;
                            return;
                        }

                        // 创建连接线
                        createConnection(startPoint, this);

                        // 重置起始点
                        startPoint.style.backgroundColor = '#4285f4';
                        startPoint = null;
                    } else {
                        // 取消选择
                        this.style.backgroundColor = '#4285f4';
                        startPoint = null;
                    }
                });

                // 输入点点击事件
                inputPoint.addEventListener('click', function() {
                    if (startPoint === null) {
                        // 开始连接
                        startPoint = this;
                        this.style.backgroundColor = '#f44336'; // 变红表示选中
                    } else if (startPoint !== this) {
                        // 不能连接两个输出点
                        if (startPoint.classList.contains('output') && this.classList.contains('output')) {
                            alert('不能连接两个输出点');
                            startPoint.style.backgroundColor = '#4285f4';
                            startPoint = null;
                            return;
                        }

                        // 不能连接两个输入点
                        if (startPoint.classList.contains('input') && this.classList.contains('input')) {
                            alert('不能连接两个输入点');
                            startPoint.style.backgroundColor = '#4285f4';
                            startPoint = null;
                            return;
                        }

                        // 创建连接线
                        createConnection(startPoint, this);

                        // 重置起始点
                        startPoint.style.backgroundColor = '#4285f4';
                        startPoint = null;
                    } else {
                        // 取消选择
                        this.style.backgroundColor = '#4285f4';
                        startPoint = null;
                    }
                });
            }

            // 创建连接线
            function createConnection(point1, point2) {
                // 确保point1是输出点，point2是输入点
                if (point1.classList.contains('input') && point2.classList.contains('output')) {
                    const temp = point1;
                    point1 = point2;
                    point2 = temp;
                }

                // 获取两点的位置
                const rect1 = point1.getBoundingClientRect();
                const rect2 = point2.getBoundingClientRect();

                // 计算画布的位置
                const canvasRect = canvasArea.getBoundingClientRect();

                // 计算连接线的起点和终点（相对于画布）
                const x1 = rect1.left + rect1.width / 2 - canvasRect.left;
                const y1 = rect1.top + rect1.height / 2 - canvasRect.top;
                const x2 = rect2.left + rect2.width / 2 - canvasRect.left;
                const y2 = rect2.top + rect2.height / 2 - canvasRect.top;

                // 计算连接线的长度和角度
                const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
                const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

                // 创建连接线
                const line = document.createElement('div');
                line.className = 'connection-line';
                line.style.width = length + 'px';
                line.style.left = x1 + 'px';
                line.style.top = y1 + 'px';
                line.style.transform = `rotate(${angle}deg)`;

                // 存储连接的点
                line.setAttribute('data-from', point1.getAttribute('data-template-id'));
                line.setAttribute('data-to', point2.getAttribute('data-template-id'));

                // 添加到画布
                canvasArea.appendChild(line);

                // 存储连接
                connections.push({
                    line: line,
                    from: point1,
                    to: point2
                });

                // 添加双击删除功能
                line.addEventListener('dblclick', function() {
                    line.remove();
                    connections = connections.filter(conn => conn.line !== line);
                });
            }

            // 更新连接线位置
            function updateConnections() {
                connections.forEach(conn => {
                    // 获取两点的位置
                    const rect1 = conn.from.getBoundingClientRect();
                    const rect2 = conn.to.getBoundingClientRect();

                    // 计算画布的位置
                    const canvasRect = canvasArea.getBoundingClientRect();

                    // 计算连接线的起点和终点（相对于画布）
                    const x1 = rect1.left + rect1.width / 2 - canvasRect.left;
                    const y1 = rect1.top + rect1.height / 2 - canvasRect.top;
                    const x2 = rect2.left + rect2.width / 2 - canvasRect.left;
                    const y2 = rect2.top + rect2.height / 2 - canvasRect.top;

                    // 计算连接线的长度和角度
                    const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
                    const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

                    // 更新连接线
                    conn.line.style.width = length + 'px';
                    conn.line.style.left = x1 + 'px';
                    conn.line.style.top = y1 + 'px';
                    conn.line.style.transform = `rotate(${angle}deg)`;
                });
            }

            // 监听模板移动，更新连接线
            document.addEventListener('mousemove', function() {
                if (connections.length > 0) {
                    updateConnections();
                }
            });

            // 功能区按钮事件
            const functionButtons = document.querySelectorAll('.function-button');

            // 全部启用按钮
            functionButtons[0].addEventListener('click', function() {
                document.querySelectorAll('.canvas-template').forEach(template => {
                    const checkbox = template.querySelector('input[type="checkbox"]');
                    const header = template.querySelector('.template-header');
                    checkbox.checked = true;
                    header.className = 'template-header active';
                });
                updateStatusBar();
            });

            // 全部取消启用按钮
            functionButtons[1].addEventListener('click', function() {
                document.querySelectorAll('.canvas-template').forEach(template => {
                    const checkbox = template.querySelector('input[type="checkbox"]');
                    const header = template.querySelector('.template-header');
                    checkbox.checked = false;
                    header.className = 'template-header inactive';
                });
                updateStatusBar();
            });

            // 清空所有子模板按钮
            functionButtons[2].addEventListener('click', function() {
                document.querySelectorAll('.canvas-template').forEach(template => {
                    const body = template.querySelector('.template-body');
                    // 保留条件行
                    const conditionRow = body.querySelector('.condition-row');
                    body.innerHTML = '';
                    body.appendChild(conditionRow);
                });
            });

            // 删除所有子模板按钮
            functionButtons[3].addEventListener('click', function() {
                if (confirm('确定要删除所有子模板吗？')) {
                    document.querySelectorAll('.canvas-template').forEach(template => {
                        template.remove();
                    });
                    updateStatusBar();
                }
            });

            // 隐藏关闭的模板按钮
            functionButtons[4].addEventListener('click', function() {
                document.querySelectorAll('.canvas-template').forEach(template => {
                    const checkbox = template.querySelector('input[type="checkbox"]');
                    if (!checkbox.checked) {
                        template.style.display = 'none';
                    }
                });
            });

            // 删除关闭的模板按钮
            functionButtons[5].addEventListener('click', function() {
                document.querySelectorAll('.canvas-template').forEach(template => {
                    const checkbox = template.querySelector('input[type="checkbox"]');
                    if (!checkbox.checked) {
                        template.remove();
                    }
                });
                updateStatusBar();
            });

            // 删除常规数据模板按钮
            functionButtons[6].addEventListener('click', function() {
                if (confirm('确定要删除所有常规数据模板吗？')) {
                    document.querySelectorAll('.canvas-template').forEach(template => {
                        const title = template.querySelector('.template-title').textContent;
                        // 检查是否为常规数据类模板
                        const isDataType = ['平面管线类', '立管类', '调压箱调压柜类', '开挖回填', '破除恢复', '防腐', '防雷防静电'].includes(title);
                        if (isDataType) {
                            template.remove();
                        }
                    });
                    updateStatusBar();
                }
            });

            // 删除数据衍生模板按钮
            functionButtons[7].addEventListener('click', function() {
                if (confirm('确定要删除所有数据衍生模板吗？')) {
                    document.querySelectorAll('.canvas-template').forEach(template => {
                        const title = template.querySelector('.template-title').textContent;
                        // 检查是否为数据衍生关联类模板
                        const isRelationType = ['警示带示踪线', '焊口探伤', '安装台班', '措施'].includes(title);
                        if (isRelationType) {
                            template.remove();
                        }
                    });
                    updateStatusBar();
                }
            });
        });
    </script>
</body>
</html>

using System.Collections.Generic;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 属性架构扩展 - 处理类和输出类节点
    /// </summary>
    public static class PropertySchemaExtensions
    {
        /// <summary>
        /// 获取处理类和输出类节点的属性架构
        /// </summary>
        public static NodePropertySchema? GetProcessingAndOutputSchema(ModuleType moduleType)
        {
            return moduleType switch
            {
                // 处理类 - 数据过滤
                ModuleType.DataFilter => new NodePropertySchema
                {
                    NodeType = "DataFilter",
                    DisplayName = "数据过滤",
                    Description = "根据条件筛选数据，支持复杂表达式和统计功能",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        // === 基础筛选设置 ===
                        {
                            "filterCondition", new PropertyDefinition
                            {
                                Name = "filterCondition",
                                Title = "筛选条件",
                                Description = "输入筛选表达式，如：score > 85 或 name == \"张三\"",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = true,
                                DefaultValue = "",
                                DataSource = DataSourceType.Manual,
                                Group = "基础设置",
                                Order = 1
                            }
                        },

                        // === 筛选选项 ===
                        {
                            "filterType", new PropertyDefinition
                            {
                                Name = "filterType",
                                Title = "筛选类型",
                                Description = "选择包含匹配项还是排除匹配项",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "include", Label = "包含匹配项" },
                                    new() { Value = "exclude", Label = "排除匹配项" }
                                },
                                DefaultValue = "include",
                                DataSource = DataSourceType.Manual,
                                Group = "筛选选项",
                                Order = 2
                            }
                        },
                        {
                            "caseSensitive", new PropertyDefinition
                            {
                                Name = "caseSensitive",
                                Title = "区分大小写",
                                Description = "字符串比较时是否区分大小写",
                                Type = PropertyType.Boolean,
                                UiWidget = "boolean",
                                DefaultValue = false,
                                DataSource = DataSourceType.Manual,
                                Group = "筛选选项",
                                Order = 3
                            }
                        },

                        // === 统计功能 ===
                        {
                            "aggregationEnabled", new PropertyDefinition
                            {
                                Name = "aggregationEnabled",
                                Title = "启用统计",
                                Description = "是否对筛选后的数据进行统计计算",
                                Type = PropertyType.Boolean,
                                UiWidget = "boolean",
                                DefaultValue = false,
                                DataSource = DataSourceType.Manual,
                                Group = "统计功能",
                                Order = 4
                            }
                        },
                        {
                            "aggregationFunction", new PropertyDefinition
                            {
                                Name = "aggregationFunction",
                                Title = "统计函数",
                                Description = "选择要执行的统计函数",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = false,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "count", Label = "计数" },
                                    new() { Value = "sum", Label = "求和" },
                                    new() { Value = "avg", Label = "平均值" },
                                    new() { Value = "min", Label = "最小值" },
                                    new() { Value = "max", Label = "最大值" }
                                },
                                DefaultValue = "count",
                                DataSource = DataSourceType.Manual,
                                Group = "统计功能",
                                Order = 5
                            }
                        },
                        {
                            "aggregationField", new PropertyDefinition
                            {
                                Name = "aggregationField",
                                Title = "统计字段",
                                Description = "要进行统计的字段名称（计数时可为空）",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = false,
                                DataSource = DataSourceType.Manual,
                                Group = "统计功能",
                                Order = 6
                            }
                        }
                    }
                },

                // 处理类 - 数据计算
                ModuleType.DataCalculation => new NodePropertySchema
                {
                    NodeType = "DataCalculation",
                    DisplayName = "数据计算",
                    Description = "对数据进行数学计算处理",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "calculationFormula", new PropertyDefinition
                            {
                                Name = "calculationFormula",
                                Title = "计算公式",
                                Description = "数据计算的数学公式",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = true,
                                DataSource = DataSourceType.Manual,
                                Group = "计算设置",
                                Order = 1
                            }
                        },
                        {
                            "precision", new PropertyDefinition
                            {
                                Name = "precision",
                                Title = "计算精度",
                                Description = "计算结果的小数位数",
                                Type = PropertyType.Number,
                                UiWidget = "number",
                                Required = true,
                                Minimum = 0,
                                Maximum = 10,
                                DefaultValue = 2,
                                DataSource = DataSourceType.Manual,
                                Group = "计算设置",
                                Order = 2
                            }
                        },
                        {
                            "roundingMode", new PropertyDefinition
                            {
                                Name = "roundingMode",
                                Title = "舍入方式",
                                Description = "数值舍入的方式",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "round", Label = "四舍五入" },
                                    new() { Value = "floor", Label = "向下取整" },
                                    new() { Value = "ceiling", Label = "向上取整" }
                                },
                                DefaultValue = "round",
                                DataSource = DataSourceType.Manual,
                                Group = "计算设置",
                                Order = 3
                            }
                        }
                    }
                },

                // 输出类 - 文件生成
                ModuleType.FileGeneration => new NodePropertySchema
                {
                    NodeType = "FileGeneration",
                    DisplayName = "文件生成",
                    Description = "生成指定格式的文件",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "outputPath", new PropertyDefinition
                            {
                                Name = "outputPath",
                                Title = "输出路径",
                                Description = "文件生成的目标路径",
                                Type = PropertyType.Directory,
                                UiWidget = "file",
                                Required = true,
                                DataSource = DataSourceType.Manual,
                                Group = "输出设置",
                                Order = 1
                            }
                        },
                        {
                            "fileName", new PropertyDefinition
                            {
                                Name = "fileName",
                                Title = "文件名称",
                                Description = "生成文件的名称",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = true,
                                DefaultValue = "output",
                                DataSource = DataSourceType.Manual,
                                Group = "输出设置",
                                Order = 2
                            }
                        },
                        {
                            "fileFormat", new PropertyDefinition
                            {
                                Name = "fileFormat",
                                Title = "文件格式",
                                Description = "输出文件的格式",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "pdf", Label = "PDF文件" },
                                    new() { Value = "excel", Label = "Excel文件" },
                                    new() { Value = "word", Label = "Word文件" },
                                    new() { Value = "txt", Label = "文本文件" }
                                },
                                DefaultValue = "pdf",
                                DataSource = DataSourceType.Manual,
                                Group = "输出设置",
                                Order = 3
                            }
                        }
                    }
                },

                // 输出类 - CAD导出
                ModuleType.CADExport => new NodePropertySchema
                {
                    NodeType = "CADExport",
                    DisplayName = "CAD导出",
                    Description = "导出到CAD图纸",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "cadVersion", new PropertyDefinition
                            {
                                Name = "cadVersion",
                                Title = "CAD版本",
                                Description = "目标CAD软件版本",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "autocad2020", Label = "AutoCAD 2020" },
                                    new() { Value = "autocad2021", Label = "AutoCAD 2021" },
                                    new() { Value = "autocad2022", Label = "AutoCAD 2022" },
                                    new() { Value = "autocad2023", Label = "AutoCAD 2023" }
                                },
                                DefaultValue = "autocad2022",
                                DataSource = DataSourceType.Manual,
                                Group = "CAD设置",
                                Order = 1
                            }
                        },
                        {
                            "layerName", new PropertyDefinition
                            {
                                Name = "layerName",
                                Title = "图层名称",
                                Description = "导出到CAD的图层名称",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = true,
                                DefaultValue = "GAS_PIPELINE",
                                DataSource = DataSourceType.Manual,
                                Group = "CAD设置",
                                Order = 2
                            }
                        },
                        {
                            "scale", new PropertyDefinition
                            {
                                Name = "scale",
                                Title = "图纸比例",
                                Description = "CAD图纸的比例",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "1:500", Label = "1:500" },
                                    new() { Value = "1:1000", Label = "1:1000" },
                                    new() { Value = "1:2000", Label = "1:2000" },
                                    new() { Value = "1:5000", Label = "1:5000" }
                                },
                                DefaultValue = "1:1000",
                                DataSource = DataSourceType.StandardLibrary,
                                Group = "CAD设置",
                                Order = 3
                            }
                        }
                    }
                },

                // 输出类 - Excel导出
                ModuleType.ExcelExport => new NodePropertySchema
                {
                    NodeType = "ExcelExport",
                    DisplayName = "Excel导出",
                    Description = "导出到Excel表格",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "templateFile", new PropertyDefinition
                            {
                                Name = "templateFile",
                                Title = "模板文件",
                                Description = "Excel模板文件路径",
                                Type = PropertyType.File,
                                UiWidget = "file",
                                Required = false,
                                DataSource = DataSourceType.Manual,
                                Group = "模板设置",
                                Order = 1
                            }
                        },
                        {
                            "sheetName", new PropertyDefinition
                            {
                                Name = "sheetName",
                                Title = "工作表名称",
                                Description = "Excel工作表的名称",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = true,
                                DefaultValue = "材料表",
                                DataSource = DataSourceType.Manual,
                                Group = "输出设置",
                                Order = 2
                            }
                        },
                        {
                            "includeHeader", new PropertyDefinition
                            {
                                Name = "includeHeader",
                                Title = "包含表头",
                                Description = "是否包含列标题",
                                Type = PropertyType.Boolean,
                                UiWidget = "boolean",
                                DefaultValue = true,
                                DataSource = DataSourceType.Manual,
                                Group = "输出设置",
                                Order = 3
                            }
                        }
                    }
                },

                // 处理类 - 标签搜索
                ModuleType.TagSearch => new NodePropertySchema
                {
                    NodeType = "TagSearch",
                    DisplayName = "标签搜索",
                    Description = "基于标签进行数据搜索",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "searchTags", new PropertyDefinition
                            {
                                Name = "searchTags",
                                Title = "搜索标签",
                                Description = "要搜索的标签关键词",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = true,
                                DataSource = DataSourceType.Manual,
                                Group = "搜索条件",
                                Order = 1
                            }
                        },
                        {
                            "matchMode", new PropertyDefinition
                            {
                                Name = "matchMode",
                                Title = "匹配模式",
                                Description = "标签匹配的模式",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "exact", Label = "精确匹配" },
                                    new() { Value = "partial", Label = "部分匹配" },
                                    new() { Value = "fuzzy", Label = "模糊匹配" }
                                },
                                DefaultValue = "partial",
                                DataSource = DataSourceType.Manual,
                                Group = "搜索选项",
                                Order = 2
                            }
                        }
                    }
                },

                // 处理类 - 数据验证
                ModuleType.DataValidation => new NodePropertySchema
                {
                    NodeType = "DataValidation",
                    DisplayName = "数据验证",
                    Description = "验证数据的完整性和正确性",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "validationRules", new PropertyDefinition
                            {
                                Name = "validationRules",
                                Title = "验证规则",
                                Description = "数据验证的规则表达式",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = true,
                                DataSource = DataSourceType.Manual,
                                Group = "验证设置",
                                Order = 1
                            }
                        },
                        {
                            "errorAction", new PropertyDefinition
                            {
                                Name = "errorAction",
                                Title = "错误处理",
                                Description = "验证失败时的处理方式",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "stop", Label = "停止处理" },
                                    new() { Value = "skip", Label = "跳过记录" },
                                    new() { Value = "log", Label = "记录错误" }
                                },
                                DefaultValue = "log",
                                DataSource = DataSourceType.Manual,
                                Group = "错误处理",
                                Order = 2
                            }
                        }
                    }
                },

                // 整理类 - 表格管理
                ModuleType.TableManager => new NodePropertySchema
                {
                    NodeType = "TableManager",
                    DisplayName = "表格管理",
                    Description = "管理和操作数据表格",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "tableOperation", new PropertyDefinition
                            {
                                Name = "tableOperation",
                                Title = "表格操作",
                                Description = "对表格执行的操作类型",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "merge", Label = "合并表格" },
                                    new() { Value = "split", Label = "拆分表格" },
                                    new() { Value = "sort", Label = "排序表格" },
                                    new() { Value = "group", Label = "分组表格" }
                                },
                                DefaultValue = "merge",
                                DataSource = DataSourceType.Manual,
                                Group = "操作设置",
                                Order = 1
                            }
                        },
                        {
                            "sortColumn", new PropertyDefinition
                            {
                                Name = "sortColumn",
                                Title = "排序列",
                                Description = "用于排序的列名",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = false,
                                DataSource = DataSourceType.Manual,
                                Group = "排序设置",
                                Order = 2
                            }
                        }
                    }
                },

                // 输出类 - 通知警报
                ModuleType.NotificationAlert => new NodePropertySchema
                {
                    NodeType = "NotificationAlert",
                    DisplayName = "通知警报",
                    Description = "发送通知和警报信息",
                    Properties = new Dictionary<string, PropertyDefinition>
                    {
                        {
                            "alertType", new PropertyDefinition
                            {
                                Name = "alertType",
                                Title = "警报类型",
                                Description = "警报的类型级别",
                                Type = PropertyType.Select,
                                UiWidget = "select",
                                Required = true,
                                Options = new List<SelectOption>
                                {
                                    new() { Value = "info", Label = "信息提示" },
                                    new() { Value = "warning", Label = "警告" },
                                    new() { Value = "error", Label = "错误" },
                                    new() { Value = "critical", Label = "严重错误" }
                                },
                                DefaultValue = "info",
                                DataSource = DataSourceType.Manual,
                                Group = "警报设置",
                                Order = 1
                            }
                        },
                        {
                            "message", new PropertyDefinition
                            {
                                Name = "message",
                                Title = "消息内容",
                                Description = "警报的消息内容",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = true,
                                DataSource = DataSourceType.Manual,
                                Group = "消息设置",
                                Order = 2
                            }
                        },
                        {
                            "recipients", new PropertyDefinition
                            {
                                Name = "recipients",
                                Title = "接收人",
                                Description = "警报消息的接收人列表",
                                Type = PropertyType.String,
                                UiWidget = "text",
                                Required = false,
                                DataSource = DataSourceType.Manual,
                                Group = "发送设置",
                                Order = 3
                            }
                        }
                    }
                },

                _ => null
            };
        }
    }
}

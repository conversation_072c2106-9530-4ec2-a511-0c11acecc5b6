namespace ProjectDigitizer.Core.Enums;

/// <summary>
/// 模块类型枚举
/// </summary>
public enum ModuleType
{
    // 输入类 - 数据源
    FileInput,          // 文件输入
    DatabaseInput,      // 数据库输入
    APIInput,           // API输入
    CADInput,           // CAD输入
    ExcelInput,         // Excel输入
    CSVInput,           // CSV输入
    XMLInput,           // XML输入
    JSONInput,          // JSON输入
    ManualDataInput,    // 手动输入数据节点

    // 常规数据类
    PipeLine,           // 平面管线类
    RiserPipe,          // 立管类
    PressureBox,        // 调压箱调压柜类
    Excavation,         // 开挖回填
    Demolition,         // 破除恢复
    AntiCorrosion,      // 防腐
    LightningProtection,// 防雷防静电

    // 数据衍生关联类
    WarningBand,        // 警示带示踪线
    WeldInspection,     // 焊口探伤
    InstallationTeam,   // 安装台班
    Measures,           // 措施

    // 触发器类
    ClickTrigger,       // 点击触发器
    AssociationTrigger, // 关联触发器
    TimedTrigger,       // 定时触发器
    FileChangeTrigger,  // 文件变化触发器
    EnvironmentTrigger, // 环境触发器

    // 处理类
    DataFilter,         // 数据过滤
    TagSearch,          // 标签搜索
    DataCalculation,    // 数据计算
    DataValidation,     // 数据验证
    DataTransform,      // 数据转换
    DataCondition,      // 数据条件
    DataMerge,          // 数据合并
    DataSort,           // 数据排序
    DataGroup,          // 数据分组
    ArrayExpansion,     // 数组展开节点
    Other,              // 其他

    // 整理类
    TableManager,       // 表格管理
    GraphicsAPI,        // 图形API
    ExcelCSV,           // Excel/CSV
    WordProcessor,      // Word处理

    // 输出类
    FileGeneration,     // 文件生成
    ManualLocation,     // 手动定位
    SpecifiedPath,      // 指定路径
    ThirdPartyAPI,      // 第三方API
    CADExport,          // CAD导出
    ExcelExport,        // Excel导出
    CSVExport,          // CSV导出
    WordExport,         // Word导出
    PPTExport,          // PPT导出
    ImageExport,        // 图片导出
    PublishRelease,     // 发布释放
    NotificationAlert,  // 通知警报
    DatabaseOutput,     // 数据库输出
    ReportGeneration,   // 报表生成
    EmailSender,        // 邮件发送
    PrintOutput,        // 打印输出
    WebServiceOutput,   // Web服务输出
    FTPUpload,          // FTP上传
    CloudStorage,       // 云存储
    DialogChat,         // 对话聊天
    OtherOutput,        // 其他输出

    // 控制类
    ConditionalBranch,  // 条件分支
    LoopProcessor,      // 循环处理
    ErrorHandler,       // 错误处理
    FlowControl,        // 流程控制
    ScriptExecutor,     // 脚本执行
    VariableManager,    // 变量管理
    StateManager,       // 状态管理
    AIAgent             // 智能体节点
}

/// <summary>
/// 节点类型分类（基于Kettle ETL设计模式）
/// </summary>
public enum NodeType
{
    /// <summary>
    /// 输入节点 - 数据源节点
    /// </summary>
    Input,

    /// <summary>
    /// 转换节点 - 数据处理节点
    /// </summary>
    Transform,

    /// <summary>
    /// 输出节点 - 数据目标节点
    /// </summary>
    Output,

    /// <summary>
    /// 控制节点 - 流程控制节点
    /// </summary>
    Control
}

/// <summary>
/// 数据来源类型枚举
/// </summary>
public enum DataSourceType
{
    ProjectInfo,    // 项目信息关联 (紫色)
    CADInfo,        // CAD信息关联 (橙色)
    ManualInput,    // 手动输入参数 (绿色)
    DefaultValue,   // 固定默认值 (黑色)
    MaterialLib,    // 材料库（字典）(蓝色)
    Standard,       // 规范与图库 (浅蓝色)
    Error          // 错误信息 (红色)
}

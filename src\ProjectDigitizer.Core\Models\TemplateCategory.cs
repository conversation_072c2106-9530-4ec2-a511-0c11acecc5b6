using System.Collections.ObjectModel;

namespace ProjectDigitizer.Core.Models;

/// <summary>
/// 模板分类
/// </summary>
public class TemplateCategory : EntityBase
{
    private string _name = string.Empty;
    private string _colorHex = "#808080"; // 默认灰色

    /// <summary>
    /// 分类名称
    /// </summary>
    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    /// <summary>
    /// 分类颜色（十六进制）
    /// </summary>
    public string ColorHex
    {
        get => _colorHex;
        set => SetProperty(ref _colorHex, value);
    }

    /// <summary>
    /// 分类中的模板项
    /// </summary>
    public ObservableCollection<TemplateItem> Items { get; } = new ObservableCollection<TemplateItem>();
}

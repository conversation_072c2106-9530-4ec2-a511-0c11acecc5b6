using System;
using System.Collections.Generic;
using System.Linq;
using ProjectDigitizer.Studio.Interfaces;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Functions
{
    /// <summary>
    /// 基础数学函数提供者
    /// </summary>
    public class BasicMathFunctions : FunctionProviderBase
    {
        public override string Name => "BasicMath";
        public override string DisplayName => "基础数学函数";
        public override string Category => "数学";
        public override string Description => "提供基础的数学计算函数，如求和、平均值、最大值、最小值等";
        public override FunctionType Type => FunctionType.Math;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "values",
                    DisplayName = "数值列表",
                    DataType = FieldDataType.Array,
                    IsRequired = true,
                    Description = "要计算的数值数组"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var values = GetArrayParameter<double>(parameters, "values");
                if (!values.Any())
                {
                    return FunctionResult.Error("数值列表不能为空");
                }

                // 这里应该根据具体的函数名称来执行不同的计算
                // 为了简化，这里只实现求和
                var result = values.Sum();
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "SUM([1, 2, 3, 4, 5]) = 15",
                "AVG([1, 2, 3, 4, 5]) = 3",
                "MAX([1, 2, 3, 4, 5]) = 5",
                "MIN([1, 2, 3, 4, 5]) = 1",
                "COUNT([1, 2, 3, 4, 5]) = 5"
            };
        }
    }

    /// <summary>
    /// 求和函数
    /// </summary>
    public class SumFunction : FunctionProviderBase
    {
        public override string Name => "SUM";
        public override string DisplayName => "求和";
        public override string Category => "数学";
        public override string Description => "计算数值列表的总和";
        public override FunctionType Type => FunctionType.Math;
        public override bool SupportsVariableParameters => true;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "values",
                    DisplayName = "数值",
                    DataType = FieldDataType.Array,
                    IsRequired = true,
                    Description = "要求和的数值列表"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var values = GetArrayParameter<double>(parameters, "values");
                if (!values.Any())
                {
                    return FunctionResult.Success(0, FieldDataType.Number);
                }

                var result = values.Sum();
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"求和计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "SUM(1, 2, 3) = 6",
                "SUM([1, 2, 3, 4, 5]) = 15",
                "SUM({field1}, {field2}, {field3})"
            };
        }
    }

    /// <summary>
    /// 平均值函数
    /// </summary>
    public class AverageFunction : FunctionProviderBase
    {
        public override string Name => "AVG";
        public override string DisplayName => "平均值";
        public override string Category => "数学";
        public override string Description => "计算数值列表的平均值";
        public override FunctionType Type => FunctionType.Statistical;
        public override bool SupportsVariableParameters => true;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "values",
                    DisplayName = "数值",
                    DataType = FieldDataType.Array,
                    IsRequired = true,
                    Description = "要计算平均值的数值列表"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var values = GetArrayParameter<double>(parameters, "values");
                if (!values.Any())
                {
                    return FunctionResult.Error("数值列表不能为空");
                }

                var result = values.Average();
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"平均值计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "AVG(1, 2, 3) = 2",
                "AVG([1, 2, 3, 4, 5]) = 3",
                "AVG({field1}, {field2}, {field3})"
            };
        }
    }

    /// <summary>
    /// 计数函数
    /// </summary>
    public class CountFunction : FunctionProviderBase
    {
        public override string Name => "COUNT";
        public override string DisplayName => "计数";
        public override string Category => "数学";
        public override string Description => "计算数值列表中非空值的数量";
        public override FunctionType Type => FunctionType.Statistical;
        public override bool SupportsVariableParameters => true;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "values",
                    DisplayName = "数值",
                    DataType = FieldDataType.Array,
                    IsRequired = true,
                    Description = "要计数的数值列表"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var values = GetArrayParameter<object>(parameters, "values");
                var count = values.Count(v => v != null);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = count,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"计数失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "COUNT(1, 2, 3) = 3",
                "COUNT([1, null, 3, 4, 5]) = 4",
                "COUNT({field1}, {field2}, {field3})"
            };
        }
    }

    /// <summary>
    /// 最大值函数
    /// </summary>
    public class MaxFunction : FunctionProviderBase
    {
        public override string Name => "MAX";
        public override string DisplayName => "最大值";
        public override string Category => "数学";
        public override string Description => "找出数值列表中的最大值";
        public override FunctionType Type => FunctionType.Math;
        public override bool SupportsVariableParameters => true;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "values",
                    DisplayName = "数值",
                    DataType = FieldDataType.Array,
                    IsRequired = true,
                    Description = "要比较的数值列表"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var values = GetArrayParameter<double>(parameters, "values");
                if (!values.Any())
                {
                    return FunctionResult.Error("数值列表不能为空");
                }

                var result = values.Max();
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"最大值计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "MAX(1, 2, 3) = 3",
                "MAX([1, 2, 3, 4, 5]) = 5",
                "MAX({field1}, {field2}, {field3})"
            };
        }
    }

    /// <summary>
    /// 最小值函数
    /// </summary>
    public class MinFunction : FunctionProviderBase
    {
        public override string Name => "MIN";
        public override string DisplayName => "最小值";
        public override string Category => "数学";
        public override string Description => "找出数值列表中的最小值";
        public override FunctionType Type => FunctionType.Math;
        public override bool SupportsVariableParameters => true;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "values",
                    DisplayName = "数值",
                    DataType = FieldDataType.Array,
                    IsRequired = true,
                    Description = "要比较的数值列表"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var values = GetArrayParameter<double>(parameters, "values");
                if (!values.Any())
                {
                    return FunctionResult.Error("数值列表不能为空");
                }

                var result = values.Min();
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"最小值计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "MIN(1, 2, 3) = 1",
                "MIN([1, 2, 3, 4, 5]) = 1",
                "MIN({field1}, {field2}, {field3})"
            };
        }
    }
}

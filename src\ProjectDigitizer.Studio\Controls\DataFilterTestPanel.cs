using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.Processors;
using ProjectDigitizer.Studio.Services;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// DataFilter 功能测试面板
    /// 用于在程序中测试新的筛选和统计功能
    /// </summary>
    public class DataFilterTestPanel : UserControl
    {
        private readonly Grid _mainGrid;
        private TextBox _expressionTextBox = null!;
        private ComboBox _functionComboBox = null!;
        private TextBox _fieldTextBox = null!;
        private CheckBox _aggregationCheckBox = null!;
        private Button _testButton = null!;
        private TextBlock _resultTextBlock = null!;
        private DataGrid _resultDataGrid = null!;
        private StatisticsDisplayWidget _statsWidget = null!;

        private readonly DataFilterProcessor _processor;
        private List<Dictionary<string, object?>> _testData;

        public DataFilterTestPanel()
        {
            _processor = new DataFilterProcessor();
            _testData = CreateSampleData();

            // 创建主网格
            _mainGrid = new Grid();
            _mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 标题
            _mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 控制面板
            _mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 按钮
            _mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 结果文本
            _mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 统计结果
            _mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // 数据表格

            CreateTitle();
            CreateControlPanel();
            CreateTestButton();
            CreateResultDisplay();
            CreateStatsDisplay();
            CreateDataGrid();

            Content = _mainGrid;

            // 设置默认样式
            Background = Brushes.White;
            BorderBrush = Brushes.LightGray;
            BorderThickness = new Thickness(1, 1, 1, 1);
            Padding = new Thickness(8, 8, 8, 8);
        }

        #region UI 创建方法

        /// <summary>
        /// 创建标题
        /// </summary>
        private void CreateTitle()
        {
            var title = new TextBlock
            {
                Text = "DataFilter 功能测试",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 16),
                HorizontalAlignment = HorizontalAlignment.Center,
                Foreground = new SolidColorBrush(Color.FromRgb(0, 120, 215))
            };

            Grid.SetRow(title, 0);
            _mainGrid.Children.Add(title);
        }

        /// <summary>
        /// 创建控制面板
        /// </summary>
        private void CreateControlPanel()
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                Margin = new Thickness(0, 0, 0, 16)
            };

            // 表达式输入
            var expressionLabel = new TextBlock
            {
                Text = "筛选表达式:",
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 4)
            };

            _expressionTextBox = new TextBox
            {
                Height = 24,
                Margin = new Thickness(0, 0, 0, 8),
                Text = "score > 85"
            };

            // 统计功能
            _aggregationCheckBox = new CheckBox
            {
                Content = "启用统计功能",
                Margin = new Thickness(0, 0, 0, 8),
                IsChecked = true
            };

            // 统计函数选择
            var functionPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 8)
            };

            var functionLabel = new TextBlock
            {
                Text = "统计函数:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            _functionComboBox = new ComboBox
            {
                Width = 100,
                Margin = new Thickness(0, 0, 8, 0)
            };
            _functionComboBox.Items.Add("count");
            _functionComboBox.Items.Add("sum");
            _functionComboBox.Items.Add("avg");
            _functionComboBox.Items.Add("min");
            _functionComboBox.Items.Add("max");
            _functionComboBox.SelectedIndex = 0;

            var fieldLabel = new TextBlock
            {
                Text = "字段:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            _fieldTextBox = new TextBox
            {
                Width = 100,
                Text = "score"
            };

            functionPanel.Children.Add(functionLabel);
            functionPanel.Children.Add(_functionComboBox);
            functionPanel.Children.Add(fieldLabel);
            functionPanel.Children.Add(_fieldTextBox);

            panel.Children.Add(expressionLabel);
            panel.Children.Add(_expressionTextBox);
            panel.Children.Add(_aggregationCheckBox);
            panel.Children.Add(functionPanel);

            Grid.SetRow(panel, 1);
            _mainGrid.Children.Add(panel);
        }

        /// <summary>
        /// 创建测试按钮
        /// </summary>
        private void CreateTestButton()
        {
            _testButton = new Button
            {
                Content = "执行测试",
                Width = 120,
                Height = 32,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 16),
                Background = new SolidColorBrush(Color.FromRgb(0, 120, 215)),
                Foreground = Brushes.White,
                FontWeight = FontWeights.SemiBold
            };

            _testButton.Click += OnTestButtonClick;

            Grid.SetRow(_testButton, 2);
            _mainGrid.Children.Add(_testButton);
        }

        /// <summary>
        /// 创建结果显示
        /// </summary>
        private void CreateResultDisplay()
        {
            _resultTextBlock = new TextBlock
            {
                Margin = new Thickness(0, 0, 0, 8),
                TextWrapping = TextWrapping.Wrap,
                FontFamily = new FontFamily("Consolas, Courier New"),
                FontSize = 11,
                Background = new SolidColorBrush(Color.FromArgb(20, 0, 0, 0)),
                Padding = new Thickness(8, 8, 8, 8),
                Text = "点击 '执行测试' 按钮开始测试..."
            };

            Grid.SetRow(_resultTextBlock, 3);
            _mainGrid.Children.Add(_resultTextBlock);
        }

        /// <summary>
        /// 创建统计显示
        /// </summary>
        private void CreateStatsDisplay()
        {
            _statsWidget = new StatisticsDisplayWidget
            {
                Margin = new Thickness(0, 0, 0, 8)
            };

            Grid.SetRow(_statsWidget, 4);
            _mainGrid.Children.Add(_statsWidget);
        }

        /// <summary>
        /// 创建数据表格
        /// </summary>
        private void CreateDataGrid()
        {
            _resultDataGrid = new DataGrid
            {
                AutoGenerateColumns = true,
                IsReadOnly = true,
                CanUserAddRows = false,
                CanUserDeleteRows = false,
                GridLinesVisibility = DataGridGridLinesVisibility.Horizontal,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                AlternatingRowBackground = new SolidColorBrush(Color.FromArgb(20, 0, 0, 0)),
                MaxHeight = 300
            };

            Grid.SetRow(_resultDataGrid, 5);
            _mainGrid.Children.Add(_resultDataGrid);
        }

        #endregion

        /// <summary>
        /// 测试按钮点击事件
        /// </summary>
        private void OnTestButtonClick(object sender, RoutedEventArgs e)
        {
            try
            {
                _testButton.IsEnabled = false;
                _testButton.Content = "执行中...";

                // 构建测试属性
                var properties = new Dictionary<string, object?>
                {
                    ["expressionMode"] = "advanced",
                    ["filterCondition"] = _expressionTextBox.Text,
                    ["caseSensitive"] = false,
                    ["filterType"] = "include",
                    ["aggregationEnabled"] = _aggregationCheckBox.IsChecked == true,
                    ["aggregationFunction"] = _functionComboBox.SelectedItem?.ToString() ?? "count",
                    ["aggregationField"] = _fieldTextBox.Text,
                    ["displayMode"] = "both"
                };

                // 执行测试
                var result = _processor.ProcessData(_testData, properties);

                // 显示结果
                DisplayResult(result);
            }
            catch (Exception ex)
            {
                _resultTextBlock.Text = $"测试失败: {ex.Message}";
                _resultTextBlock.Foreground = Brushes.Red;
            }
            finally
            {
                _testButton.IsEnabled = true;
                _testButton.Content = "执行测试";
            }
        }

        /// <summary>
        /// 显示测试结果
        /// </summary>
        private void DisplayResult(DataFilterResult result)
        {
            if (result.IsSuccess)
            {
                _resultTextBlock.Foreground = Brushes.Green;
                _resultTextBlock.Text = $"✅ 测试成功!\n{result.GetSummary()}";

                // 显示筛选后的数据
                _resultDataGrid.ItemsSource = result.FilteredData;

                // 显示统计结果
                if (result.AggregationResult != null)
                {
                    _statsWidget.SetResults(result.AggregationResult);
                }
                else
                {
                    _statsWidget.Clear();
                }
            }
            else
            {
                _resultTextBlock.Foreground = Brushes.Red;
                _resultTextBlock.Text = $"❌ 测试失败: {result.ErrorMessage}";
                _resultDataGrid.ItemsSource = null;
                _statsWidget.Clear();
            }
        }

        /// <summary>
        /// 创建示例数据
        /// </summary>
        private List<Dictionary<string, object?>> CreateSampleData()
        {
            return new List<Dictionary<string, object?>>
            {
                new() { ["id"] = 1, ["name"] = "张三", ["score"] = 95, ["class"] = "A", ["age"] = 20, ["status"] = "VIP" },
                new() { ["id"] = 2, ["name"] = "李四", ["score"] = 87, ["class"] = "B", ["age"] = 19, ["status"] = "Normal" },
                new() { ["id"] = 3, ["name"] = "王五", ["score"] = 92, ["class"] = "A", ["age"] = 21, ["status"] = "Normal" },
                new() { ["id"] = 4, ["name"] = "赵六", ["score"] = 78, ["class"] = "C", ["age"] = 18, ["status"] = "Normal" },
                new() { ["id"] = 5, ["name"] = "钱七", ["score"] = 88, ["class"] = "B", ["age"] = 20, ["status"] = "VIP" },
                new() { ["id"] = 6, ["name"] = "孙八", ["score"] = 82, ["class"] = "A", ["age"] = 19, ["status"] = "Normal" },
                new() { ["id"] = 7, ["name"] = "周九", ["score"] = 96, ["class"] = "A", ["age"] = 22, ["status"] = "VIP" },
                new() { ["id"] = 8, ["name"] = "吴十", ["score"] = 75, ["class"] = "C", ["age"] = 20, ["status"] = "Normal" }
            };
        }

        /// <summary>
        /// 设置测试数据
        /// </summary>
        public void SetTestData(List<Dictionary<string, object?>> data)
        {
            _testData = data ?? CreateSampleData();
        }
    }
}

using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProjectDigitizer.Core.Models;

/// <summary>
/// 实体基类
/// </summary>
public abstract class EntityBase : IEntity, INotifyPropertyChanged
{
    private string _id;

    protected EntityBase()
    {
        _id = Guid.NewGuid().ToString();
    }

    protected EntityBase(string id)
    {
        _id = id ?? throw new ArgumentNullException(nameof(id));
    }

    /// <summary>
    /// 实体唯一标识符
    /// </summary>
    public string Id
    {
        get => _id;
        protected set => SetProperty(ref _id, value);
    }

    #region INotifyPropertyChanged Implementation

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    #endregion

    #region Equality

    public override bool Equals(object? obj)
    {
        if (obj is not EntityBase other)
            return false;

        if (ReferenceEquals(this, other))
            return true;

        return Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    public static bool operator ==(EntityBase? left, EntityBase? right)
    {
        return Equals(left, right);
    }

    public static bool operator !=(EntityBase? left, EntityBase? right)
    {
        return !Equals(left, right);
    }

    #endregion
}

using ProjectDigitizer.Core.Enums;

namespace ProjectDigitizer.Core.Models;

/// <summary>
/// 项目文件数据结构
/// </summary>
public class ProjectFile : EntityBase
{
    private ProjectInfo _projectInfo = new();
    private CanvasData _canvasData = new();
    private List<TemplateData> _templates = new();
    private string _version = "1.0";
    private DateTime _createdTime = DateTime.Now;
    private DateTime _lastModifiedTime = DateTime.Now;

    /// <summary>
    /// 项目信息
    /// </summary>
    public ProjectInfo ProjectInfo
    {
        get => _projectInfo;
        set => SetProperty(ref _projectInfo, value);
    }

    /// <summary>
    /// 画布数据
    /// </summary>
    public CanvasData CanvasData
    {
        get => _canvasData;
        set => SetProperty(ref _canvasData, value);
    }

    /// <summary>
    /// 模板列表
    /// </summary>
    public List<TemplateData> Templates
    {
        get => _templates;
        set => SetProperty(ref _templates, value);
    }

    /// <summary>
    /// 文件版本
    /// </summary>
    public string Version
    {
        get => _version;
        set => SetProperty(ref _version, value);
    }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime
    {
        get => _createdTime;
        set => SetProperty(ref _createdTime, value);
    }

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModifiedTime
    {
        get => _lastModifiedTime;
        set => SetProperty(ref _lastModifiedTime, value);
    }
}

/// <summary>
/// 项目基本信息
/// </summary>
public class ProjectInfo : EntityBase
{
    private string _name = "新项目";
    private string _description = "";
    private string _creator = Environment.UserName;
    private DateTime _createTime = DateTime.Now;
    private string _phase = "设计阶段";
    private string _projectNumber = "";
    private List<SubProject> _subProjects = new();
    private ProjectStatistics _statistics = new();

    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    public string Description
    {
        get => _description;
        set => SetProperty(ref _description, value);
    }

    public string Creator
    {
        get => _creator;
        set => SetProperty(ref _creator, value);
    }

    public DateTime CreateTime
    {
        get => _createTime;
        set => SetProperty(ref _createTime, value);
    }

    public string Phase
    {
        get => _phase;
        set => SetProperty(ref _phase, value);
    }

    public string ProjectNumber
    {
        get => _projectNumber;
        set => SetProperty(ref _projectNumber, value);
    }

    public List<SubProject> SubProjects
    {
        get => _subProjects;
        set => SetProperty(ref _subProjects, value);
    }

    /// <summary>
    /// 项目统计信息
    /// </summary>
    public ProjectStatistics Statistics
    {
        get => _statistics;
        set => SetProperty(ref _statistics, value);
    }
}

/// <summary>
/// 子项目信息
/// </summary>
public class SubProject : EntityBase
{
    private string _name = "";
    private string _number = "";
    private int _order = 0;
    private string _parentId = "";
    private Dictionary<string, object> _properties = new();

    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    public string Number
    {
        get => _number;
        set => SetProperty(ref _number, value);
    }

    public int Order
    {
        get => _order;
        set => SetProperty(ref _order, value);
    }

    public string ParentId
    {
        get => _parentId;
        set => SetProperty(ref _parentId, value);
    }

    public Dictionary<string, object> Properties
    {
        get => _properties;
        set => SetProperty(ref _properties, value);
    }
}

/// <summary>
/// 项目统计信息
/// </summary>
public class ProjectStatistics
{
    public double TotalPipelineLength { get; set; } = 0;
    public int UserCount { get; set; } = 0;
    public int CommunityCount { get; set; } = 0;
    public int PressureBoxCount { get; set; } = 0;
    public Dictionary<string, object> CustomStatistics { get; set; } = new();
}

/// <summary>
/// 画布数据
/// </summary>
public class CanvasData
{
    public List<NodeData> Nodes { get; set; } = new();
    public List<ConnectionData> Connections { get; set; } = new();
    public CanvasSettings Settings { get; set; } = new();
    public string CurrentTemplate { get; set; } = "默认模板";
}

/// <summary>
/// 节点数据
/// </summary>
public class NodeData : EntityBase
{
    private string _title = "";
    private ModuleType _type;
    private double _x;
    private double _y;
    private bool _isEnabled = true;
    private bool _isLightBulbOn = true;
    private bool _isExpanded = false;
    private Dictionary<string, object?> _propertyValues = new();
    private List<string> _conditions = new();
    private List<ConnectorData> _inputs = new();
    private List<ConnectorData> _outputs = new();

    public string Title
    {
        get => _title;
        set => SetProperty(ref _title, value);
    }

    public ModuleType Type
    {
        get => _type;
        set => SetProperty(ref _type, value);
    }

    public double X
    {
        get => _x;
        set => SetProperty(ref _x, value);
    }

    public double Y
    {
        get => _y;
        set => SetProperty(ref _y, value);
    }

    public bool IsEnabled
    {
        get => _isEnabled;
        set => SetProperty(ref _isEnabled, value);
    }

    public bool IsLightBulbOn
    {
        get => _isLightBulbOn;
        set => SetProperty(ref _isLightBulbOn, value);
    }

    public bool IsExpanded
    {
        get => _isExpanded;
        set => SetProperty(ref _isExpanded, value);
    }

    public Dictionary<string, object?> PropertyValues
    {
        get => _propertyValues;
        set => SetProperty(ref _propertyValues, value);
    }

    public List<string> Conditions
    {
        get => _conditions;
        set => SetProperty(ref _conditions, value);
    }

    public List<ConnectorData> Inputs
    {
        get => _inputs;
        set => SetProperty(ref _inputs, value);
    }

    public List<ConnectorData> Outputs
    {
        get => _outputs;
        set => SetProperty(ref _outputs, value);
    }
}

/// <summary>
/// 连接器数据
/// </summary>
public class ConnectorData : EntityBase
{
    private string _title = "";
    private string _dataType = "";
    private bool _isInput;

    public string Title
    {
        get => _title;
        set => SetProperty(ref _title, value);
    }

    public string DataType
    {
        get => _dataType;
        set => SetProperty(ref _dataType, value);
    }

    public bool IsInput
    {
        get => _isInput;
        set => SetProperty(ref _isInput, value);
    }
}

/// <summary>
/// 连接线数据
/// </summary>
public class ConnectionData : EntityBase
{
    private string _sourceNodeId = "";
    private string _sourceConnectorId = "";
    private string _targetNodeId = "";
    private string _targetConnectorId = "";
    private bool _isEnabled = true;

    public string SourceNodeId
    {
        get => _sourceNodeId;
        set => SetProperty(ref _sourceNodeId, value);
    }

    public string SourceConnectorId
    {
        get => _sourceConnectorId;
        set => SetProperty(ref _sourceConnectorId, value);
    }

    public string TargetNodeId
    {
        get => _targetNodeId;
        set => SetProperty(ref _targetNodeId, value);
    }

    public string TargetConnectorId
    {
        get => _targetConnectorId;
        set => SetProperty(ref _targetConnectorId, value);
    }

    public bool IsEnabled
    {
        get => _isEnabled;
        set => SetProperty(ref _isEnabled, value);
    }
}

/// <summary>
/// 画布设置
/// </summary>
public class CanvasSettings
{
    public double ZoomLevel { get; set; } = 1.0;
    public double OffsetX { get; set; } = 0;
    public double OffsetY { get; set; } = 0;
    public bool ShowGrid { get; set; } = true;
    public bool SnapToGrid { get; set; } = true;
    public double GridSize { get; set; } = 20;
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

/// <summary>
/// 模板数据
/// </summary>
public class TemplateData : EntityBase
{
    private string _name = "";
    private string _description = "";
    private string _category = "";
    private DateTime _createdTime = DateTime.Now;
    private string _creator = "";
    private List<NodeData> _nodes = new();
    private List<ConnectionData> _connections = new();
    private Dictionary<string, object> _metadata = new();

    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    public string Description
    {
        get => _description;
        set => SetProperty(ref _description, value);
    }

    public string Category
    {
        get => _category;
        set => SetProperty(ref _category, value);
    }

    public DateTime CreatedTime
    {
        get => _createdTime;
        set => SetProperty(ref _createdTime, value);
    }

    public string Creator
    {
        get => _creator;
        set => SetProperty(ref _creator, value);
    }

    public List<NodeData> Nodes
    {
        get => _nodes;
        set => SetProperty(ref _nodes, value);
    }

    public List<ConnectionData> Connections
    {
        get => _connections;
        set => SetProperty(ref _connections, value);
    }

    public Dictionary<string, object> Metadata
    {
        get => _metadata;
        set => SetProperty(ref _metadata, value);
    }
}

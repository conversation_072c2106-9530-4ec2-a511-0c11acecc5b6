using ProjectDigitizer.Core.Models;

namespace ProjectDigitizer.Core.Interfaces;

/// <summary>
/// 项目文件服务接口
/// </summary>
public interface IProjectFileService
{
    /// <summary>
    /// 保存项目文件
    /// </summary>
    /// <param name="projectFile">项目文件数据</param>
    /// <param name="filePath">文件路径</param>
    Task SaveProjectAsync(ProjectFile projectFile, string filePath);

    /// <summary>
    /// 加载项目文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>项目文件数据</returns>
    Task<ProjectFile> LoadProjectAsync(string filePath);

    /// <summary>
    /// 创建项目文件从画布数据
    /// </summary>
    /// <param name="canvasData">画布数据</param>
    /// <param name="projectInfo">项目信息</param>
    /// <returns>项目文件</returns>
    ProjectFile CreateProjectFileFromCanvas(CanvasData canvasData, ProjectInfo projectInfo);

    /// <summary>
    /// 保存模板文件
    /// </summary>
    /// <param name="templateData">模板数据</param>
    /// <param name="filePath">文件路径</param>
    Task SaveTemplateAsync(TemplateData templateData, string filePath);

    /// <summary>
    /// 加载模板文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>模板数据</returns>
    Task<TemplateData> LoadTemplateAsync(string filePath);

    /// <summary>
    /// 验证项目文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateProjectFileAsync(string filePath);

    /// <summary>
    /// 获取项目文件信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>项目信息</returns>
    Task<ProjectInfo?> GetProjectInfoAsync(string filePath);
}

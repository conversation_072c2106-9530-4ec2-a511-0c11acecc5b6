using System;
using System.Windows;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 连接器数据类型枚举
    /// </summary>
    public enum ConnectorDataType
    {
        /// <summary>通用类型 - 可连接任何类型</summary>
        Any,
        /// <summary>数值型数据</summary>
        Number,
        /// <summary>文本型数据</summary>
        Text,
        /// <summary>布尔型数据</summary>
        <PERSON>olean,
        /// <summary>文件路径</summary>
        File,
        /// <summary>几何数据</summary>
        Geometry,
        /// <summary>控制流</summary>
        Control
    }

    /// <summary>
    /// 连接器视图模型
    /// </summary>
    public class ConnectorViewModel : ViewModelBase
    {
        private Point _anchor;
        private bool _isConnected;
        private string? _title;
        private bool _isInput;
        private object? _node;
        private string _id;
        private string _dataType;
        private ConnectorDataType _connectorDataType;
        private bool _isCompatible;
        private bool _isHighlighted;
        private int _connectionCount;
        private bool _supportsMultipleConnections = true;

        /// <summary>
        /// 连接器锚点位置
        /// </summary>
        public Point Anchor
        {
            get => _anchor;
            set => SetProperty(ref _anchor, value);
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected
        {
            get => _isConnected;
            set => SetProperty(ref _isConnected, value);
        }

        /// <summary>
        /// 连接数量
        /// </summary>
        public int ConnectionCount
        {
            get => _connectionCount;
            set
            {
                if (SetProperty(ref _connectionCount, value))
                {
                    // 更新IsConnected状态
                    IsConnected = value > 0;
                    OnPropertyChanged(nameof(HasMultipleConnections));
                }
            }
        }

        /// <summary>
        /// 是否有多个连接
        /// </summary>
        public bool HasMultipleConnections => ConnectionCount > 1;

        /// <summary>
        /// 是否支持多连接
        /// </summary>
        public bool SupportsMultipleConnections
        {
            get => _supportsMultipleConnections;
            set => SetProperty(ref _supportsMultipleConnections, value);
        }

        /// <summary>
        /// 连接列表
        /// </summary>
        public List<ConnectionViewModel> Connections { get; } = new List<ConnectionViewModel>();

        /// <summary>
        /// 连接器标题
        /// </summary>
        public string? Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        /// <summary>
        /// 是否为输入连接器
        /// </summary>
        public bool IsInput
        {
            get => _isInput;
            set => SetProperty(ref _isInput, value);
        }

        /// <summary>
        /// 是否为输出连接器
        /// </summary>
        public bool IsOutput => !IsInput;

        /// <summary>
        /// 所属节点
        /// </summary>
        public object? Node
        {
            get => _node;
            set => SetProperty(ref _node, value);
        }

        /// <summary>
        /// 连接器唯一标识
        /// </summary>
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// 数据类型（字符串形式，保持向后兼容）
        /// </summary>
        public string DataType
        {
            get => _dataType;
            set => SetProperty(ref _dataType, value);
        }

        /// <summary>
        /// 连接器数据类型（枚举形式）
        /// </summary>
        public ConnectorDataType ConnectorDataType
        {
            get => _connectorDataType;
            set
            {
                SetProperty(ref _connectorDataType, value);
                // 同步更新字符串形式的DataType
                _dataType = value.ToString();
                OnPropertyChanged(nameof(DataType));
            }
        }

        /// <summary>
        /// 是否与当前拖拽操作兼容
        /// </summary>
        public bool IsCompatible
        {
            get => _isCompatible;
            set => SetProperty(ref _isCompatible, value);
        }

        /// <summary>
        /// 是否高亮显示（用于兼容性提示）
        /// </summary>
        public bool IsHighlighted
        {
            get => _isHighlighted;
            set => SetProperty(ref _isHighlighted, value);
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ConnectorViewModel()
        {
            _id = Guid.NewGuid().ToString();
            _dataType = "Any";
            _connectorDataType = ConnectorDataType.Any;
            _isCompatible = true;
            _isHighlighted = false;
        }

        /// <summary>
        /// 检查是否可以与另一个连接器连接
        /// </summary>
        /// <param name="other">目标连接器</param>
        /// <returns>是否可以连接</returns>
        public bool CanConnectTo(ConnectorViewModel other)
        {
            if (other == null) return false;

            // 不能连接到自己
            if (this == other) return false;

            // 不能连接到同一个节点的连接器
            if (Node == other.Node) return false;

            // 输入连接器只能连接到输出连接器
            if (IsInput == other.IsInput) return false;

            // 检查数据类型兼容性
            return IsDataTypeCompatible(other.ConnectorDataType);
        }

        /// <summary>
        /// 检查数据类型是否兼容
        /// </summary>
        /// <param name="otherType">其他连接器的数据类型</param>
        /// <returns>是否兼容</returns>
        private bool IsDataTypeCompatible(ConnectorDataType otherType)
        {
            // Any类型可以连接任何类型
            if (ConnectorDataType == ConnectorDataType.Any || otherType == ConnectorDataType.Any)
                return true;

            // 相同类型可以连接
            if (ConnectorDataType == otherType)
                return true;

            // 数值类型可以连接到文本类型（自动转换）
            if ((ConnectorDataType == ConnectorDataType.Number && otherType == ConnectorDataType.Text) ||
                (ConnectorDataType == ConnectorDataType.Text && otherType == ConnectorDataType.Number))
                return true;

            return false;
        }

        /// <summary>
        /// 添加连接
        /// </summary>
        /// <param name="connection">连接对象</param>
        public void AddConnection(ConnectionViewModel connection)
        {
            if (!Connections.Contains(connection))
            {
                Connections.Add(connection);
                ConnectionCount = Connections.Count;

                // 为连接分配序号（基于当前连接器的连接数量）
                connection.ConnectionIndex = ConnectionCount;

                System.Diagnostics.Debug.WriteLine($"连接器 {Title} 添加连接，当前连接数: {ConnectionCount}, HasMultipleConnections: {HasMultipleConnections}");
            }
        }

        /// <summary>
        /// 移除连接
        /// </summary>
        /// <param name="connection">连接对象</param>
        public void RemoveConnection(ConnectionViewModel connection)
        {
            if (Connections.Remove(connection))
            {
                ConnectionCount = Connections.Count;
            }
        }

        /// <summary>
        /// 清除所有连接
        /// </summary>
        public void ClearConnections()
        {
            Connections.Clear();
            ConnectionCount = 0;
        }

        /// <summary>
        /// 检查是否可以添加新连接
        /// </summary>
        /// <returns>是否可以添加</returns>
        public bool CanAddConnection()
        {
            return SupportsMultipleConnections || ConnectionCount == 0;
        }
    }
}

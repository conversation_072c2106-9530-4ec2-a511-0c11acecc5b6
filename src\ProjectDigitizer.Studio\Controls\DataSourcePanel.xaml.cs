using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Win32;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 数据来源面板交互逻辑
    /// </summary>
    public partial class DataSourcePanel : UserControl, INotifyPropertyChanged
    {
        private DataSourceMode _dataSourceMode = DataSourceMode.NodeOutput;
        private string _externalFilePath = string.Empty;
        private string _searchText = string.Empty;
        private ObservableCollection<FieldInfo> _availableFields = new();
        private ObservableCollection<FieldInfo> _filteredFields = new();
        private ModuleNodeViewModel? _currentNode;

        /// <summary>数据来源模式</summary>
        public DataSourceMode DataSourceMode
        {
            get => _dataSourceMode;
            set
            {
                if (SetProperty(ref _dataSourceMode, value))
                {
                    OnPropertyChanged(nameof(IsExternalFileMode));
                    OnPropertyChanged(nameof(DataSourceModeText));
                    DataSourceModeChanged?.Invoke(this, new DataSourceChangedEventArgs(
                        _dataSourceMode == DataSourceMode.NodeOutput ? DataSourceMode.ExternalFile : DataSourceMode.NodeOutput,
                        _dataSourceMode,
                        _availableFields.ToList()));
                }
            }
        }

        /// <summary>是否为外部文件模式</summary>
        public bool IsExternalFileMode
        {
            get => _dataSourceMode == DataSourceMode.ExternalFile;
            set => DataSourceMode = value ? DataSourceMode.ExternalFile : DataSourceMode.NodeOutput;
        }

        /// <summary>数据来源模式文本</summary>
        public string DataSourceModeText =>
            _dataSourceMode == DataSourceMode.NodeOutput ? "节点数据" : "外部文件";

        /// <summary>外部文件路径</summary>
        public string ExternalFilePath
        {
            get => _externalFilePath;
            set
            {
                if (SetProperty(ref _externalFilePath, value))
                {
                    ExternalFilePathChanged?.Invoke(this, value);
                }
            }
        }

        /// <summary>搜索文本</summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    FilterFields();
                }
            }
        }

        /// <summary>可用字段列表</summary>
        public ObservableCollection<FieldInfo> AvailableFields
        {
            get => _availableFields;
            set
            {
                if (SetProperty(ref _availableFields, value))
                {
                    FilterFields();
                    UpdateFieldCounts();
                }
            }
        }

        /// <summary>过滤后的字段列表</summary>
        public ObservableCollection<FieldInfo> FilteredFields
        {
            get => _filteredFields;
            private set => SetProperty(ref _filteredFields, value);
        }

        /// <summary>总字段数</summary>
        public int TotalFieldCount => _availableFields.Count;

        /// <summary>已连接字段数</summary>
        public int ConnectedFieldCount => _availableFields.Count(f => f.IsConnected);

        /// <summary>已引用字段数</summary>
        public int ReferencedFieldCount => _availableFields.Count(f => f.IsReferenced);

        /// <summary>是否有字段</summary>
        public bool HasFields => _filteredFields.Any();

        /// <summary>空状态消息</summary>
        public string EmptyStateMessage =>
            _dataSourceMode == DataSourceMode.NodeOutput
                ? "请连接数据源节点"
                : string.IsNullOrEmpty(_externalFilePath)
                    ? "请选择数据文件"
                    : "文件中没有可用字段";

        /// <summary>当前节点</summary>
        public ModuleNodeViewModel? CurrentNode
        {
            get => _currentNode;
            set
            {
                _currentNode = value;
                if (_dataSourceMode == DataSourceMode.NodeOutput)
                {
                    LoadFieldsFromNode();
                }
            }
        }

        /// <summary>数据来源模式变化事件</summary>
        public event EventHandler<DataSourceChangedEventArgs>? DataSourceModeChanged;

        /// <summary>外部文件路径变化事件</summary>
        public event EventHandler<string>? ExternalFilePathChanged;

        /// <summary>字段双击事件</summary>
        public event EventHandler<FieldInfo>? FieldDoubleClicked;

        /// <summary>字段添加到已选事件</summary>
        public event EventHandler<FieldInfo>? FieldAddedToSelected;

        public DataSourcePanel()
        {
            InitializeComponent();
            DataContext = this;
            InitializeEventHandlers();
        }

        /// <summary>
        /// 初始化事件处理器
        /// </summary>
        private void InitializeEventHandlers()
        {
            BrowseFileButton.Click += OnBrowseFileClick;
            
            // 监听字段状态变化
            _availableFields.CollectionChanged += (s, e) =>
            {
                if (e.NewItems != null)
                {
                    foreach (FieldInfo field in e.NewItems)
                    {
                        field.PropertyChanged += OnFieldPropertyChanged;
                    }
                }
                if (e.OldItems != null)
                {
                    foreach (FieldInfo field in e.OldItems)
                    {
                        field.PropertyChanged -= OnFieldPropertyChanged;
                    }
                }
                UpdateFieldCounts();
            };
        }

        /// <summary>
        /// 字段属性变化处理
        /// </summary>
        private void OnFieldPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(FieldInfo.IsConnected) || 
                e.PropertyName == nameof(FieldInfo.IsReferenced))
            {
                UpdateFieldCounts();
            }
        }

        /// <summary>
        /// 更新字段统计数量
        /// </summary>
        private void UpdateFieldCounts()
        {
            OnPropertyChanged(nameof(TotalFieldCount));
            OnPropertyChanged(nameof(ConnectedFieldCount));
            OnPropertyChanged(nameof(ReferencedFieldCount));
            OnPropertyChanged(nameof(HasFields));
            OnPropertyChanged(nameof(EmptyStateMessage));
        }

        /// <summary>
        /// 过滤字段
        /// </summary>
        private void FilterFields()
        {
            if (string.IsNullOrWhiteSpace(_searchText))
            {
                FilteredFields = new ObservableCollection<FieldInfo>(_availableFields);
            }
            else
            {
                var searchLower = _searchText.ToLower();
                var filtered = _availableFields.Where(f =>
                    f.Name.ToLower().Contains(searchLower) ||
                    f.DisplayName.ToLower().Contains(searchLower) ||
                    f.Description.ToLower().Contains(searchLower)).ToList();
                FilteredFields = new ObservableCollection<FieldInfo>(filtered);
            }
            
            OnPropertyChanged(nameof(HasFields));
            OnPropertyChanged(nameof(EmptyStateMessage));
        }

        /// <summary>
        /// 从节点加载字段
        /// </summary>
        private void LoadFieldsFromNode()
        {
            AvailableFields.Clear();

            if (_currentNode?.Inputs == null)
                return;

            // TODO: 实现从连接的节点获取字段信息
            // 这里需要根据实际的节点连接逻辑来实现
            foreach (var input in _currentNode.Inputs.Where(i => i.IsConnected))
            {
                // 示例字段，实际应该从连接的源节点获取
                var sampleFields = CreateSampleFields(input.Title ?? "输入");
                foreach (var field in sampleFields)
                {
                    field.SourceType = "node";
                    field.SourceId = input.Id;
                    field.IsConnected = true;
                    AvailableFields.Add(field);
                }
            }
        }

        /// <summary>
        /// 从外部文件加载字段
        /// </summary>
        private void LoadFieldsFromFile(string filePath)
        {
            AvailableFields.Clear();

            if (string.IsNullOrEmpty(filePath))
                return;

            try
            {
                // TODO: 实现从外部文件读取字段信息
                // 根据文件扩展名选择不同的读取器
                var extension = System.IO.Path.GetExtension(filePath).ToLower();
                switch (extension)
                {
                    case ".csv":
                        LoadFieldsFromCsv(filePath);
                        break;
                    case ".xlsx":
                    case ".xls":
                        LoadFieldsFromExcel(filePath);
                        break;
                    case ".json":
                        LoadFieldsFromJson(filePath);
                        break;
                    default:
                        // 暂时创建示例字段
                        var sampleFields = CreateSampleFields("文件");
                        foreach (var field in sampleFields)
                        {
                            field.SourceType = "file";
                            field.SourceId = filePath;
                            AvailableFields.Add(field);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"读取文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 创建示例字段（临时实现）
        /// </summary>
        private List<FieldInfo> CreateSampleFields(string prefix)
        {
            return new List<FieldInfo>
            {
                new FieldInfo
                {
                    Name = $"{prefix}_id",
                    DisplayName = $"{prefix} ID",
                    DataType = FieldDataType.Number,
                    Description = "唯一标识符",
                    SampleValue = 1001
                },
                new FieldInfo
                {
                    Name = $"{prefix}_name",
                    DisplayName = $"{prefix} 名称",
                    DataType = FieldDataType.Text,
                    Description = "名称字段",
                    SampleValue = "示例名称"
                },
                new FieldInfo
                {
                    Name = $"{prefix}_value",
                    DisplayName = $"{prefix} 数值",
                    DataType = FieldDataType.Number,
                    Description = "数值字段",
                    SampleValue = 123.45
                },
                new FieldInfo
                {
                    Name = $"{prefix}_enabled",
                    DisplayName = $"{prefix} 启用",
                    DataType = FieldDataType.Boolean,
                    Description = "布尔字段",
                    SampleValue = true
                }
            };
        }

        /// <summary>
        /// 从CSV文件加载字段
        /// </summary>
        private void LoadFieldsFromCsv(string filePath)
        {
            // TODO: 实现CSV字段读取
        }

        /// <summary>
        /// 从Excel文件加载字段
        /// </summary>
        private void LoadFieldsFromExcel(string filePath)
        {
            // TODO: 实现Excel字段读取
        }

        /// <summary>
        /// 从JSON文件加载字段
        /// </summary>
        private void LoadFieldsFromJson(string filePath)
        {
            // TODO: 实现JSON字段读取
        }

        /// <summary>
        /// 浏览文件按钮点击处理
        /// </summary>
        private void OnBrowseFileClick(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "选择数据文件",
                Filter = "所有支持的文件|*.csv;*.xlsx;*.xls;*.json|CSV文件|*.csv|Excel文件|*.xlsx;*.xls|JSON文件|*.json|所有文件|*.*",
                FilterIndex = 1
            };

            if (dialog.ShowDialog() == true)
            {
                ExternalFilePath = dialog.FileName;
                LoadFieldsFromFile(dialog.FileName);
            }
        }

        /// <summary>
        /// 字段双击处理
        /// </summary>
        private void OnFieldDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (sender is TreeViewItem item && item.DataContext is FieldInfo field)
            {
                FieldDoubleClicked?.Invoke(this, field);
            }
        }

        /// <summary>
        /// 字段右键处理
        /// </summary>
        private void OnFieldRightClick(object sender, MouseButtonEventArgs e)
        {
            if (sender is TreeViewItem item)
            {
                item.IsSelected = true;
            }
        }

        /// <summary>
        /// 添加到已选字段菜单项点击
        /// </summary>
        private void OnAddToSelectedClick(object sender, RoutedEventArgs e)
        {
            if (FieldTreeView.SelectedItem is FieldInfo selectedField)
            {
                FieldAddedToSelected?.Invoke(this, selectedField);
            }
        }

        /// <summary>
        /// 查看字段详情菜单项点击
        /// </summary>
        private void OnViewFieldDetailsClick(object sender, RoutedEventArgs e)
        {
            if (FieldTreeView.SelectedItem is FieldInfo selectedField)
            {
                // TODO: 显示字段详情对话框
                var details = $"字段名称: {selectedField.DisplayName}\n" +
                             $"数据类型: {selectedField.DataType}\n" +
                             $"描述: {selectedField.Description}\n" +
                             $"示例值: {selectedField.SampleValue}\n" +
                             $"来源: {selectedField.SourceType}";
                MessageBox.Show(details, "字段详情", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 刷新字段列表菜单项点击
        /// </summary>
        private void OnRefreshFieldsClick(object sender, RoutedEventArgs e)
        {
            if (_dataSourceMode == DataSourceMode.NodeOutput)
            {
                LoadFieldsFromNode();
            }
            else if (!string.IsNullOrEmpty(_externalFilePath))
            {
                LoadFieldsFromFile(_externalFilePath);
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}

using System;
using System.Windows;
using ProjectDigitizer.Studio.Controls;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio
{
    /// <summary>
    /// 现代化属性面板测试工具
    /// </summary>
    public static class TestModernPropertyPanel
    {
        /// <summary>
        /// 创建测试窗口
        /// </summary>
        public static void ShowTestWindow()
        {
            try
            {
                // 创建测试窗口
                var testWindow = new Window
                {
                    Title = "现代化属性面板测试",
                    Width = 400,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };

                // 创建测试节点
                var testNode = CreateTestDataFilterNode();

                // 创建现代化属性面板
                var modernPanel = new ModernPropertyPanel();
                modernPanel.SetNode(testNode);

                // 设置窗口内容
                testWindow.Content = modernPanel;

                // 显示窗口
                testWindow.Show();

                Console.WriteLine("现代化属性面板测试窗口已打开");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建测试窗口失败: {ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 创建测试用的 DataFilter 节点
        /// </summary>
        private static ModuleNodeViewModel CreateTestDataFilterNode()
        {
            // 创建模块模型
            var module = new ModuleModel
            {
                Type = ModuleType.DataFilter,
                Name = "测试数据过滤器",
                Description = "用于测试现代化属性面板的数据过滤节点",
                IsEnabled = true,
                IsVisible = true
            };

            // 创建节点视图模型
            var nodeViewModel = new ModuleNodeViewModel
            {
                Module = module,
                Title = "测试数据过滤器",
                Location = new Point(100, 100)
            };

            // 设置一些测试属性值
            nodeViewModel.PropertyValues.SetValue("filterCondition", "score > 85");
            nodeViewModel.PropertyValues.SetValue("filterType", "include");
            nodeViewModel.PropertyValues.SetValue("caseSensitive", false);
            nodeViewModel.PropertyValues.SetValue("aggregationEnabled", true);
            nodeViewModel.PropertyValues.SetValue("aggregationFunction", "avg");
            nodeViewModel.PropertyValues.SetValue("aggregationField", "score");

            return nodeViewModel;
        }

        /// <summary>
        /// 测试属性面板管理器
        /// </summary>
        public static void TestPropertyPanelManager()
        {
            try
            {
                // 创建测试窗口
                var testWindow = new Window
                {
                    Title = "属性面板管理器测试",
                    Width = 400,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };

                // 创建容器
                var container = new System.Windows.Controls.ContentControl();
                testWindow.Content = container;

                // 创建属性面板管理器
                var panelManager = new PropertyPanelManager(container);

                // 创建测试节点
                var dataFilterNode = CreateTestDataFilterNode();
                var otherNode = CreateTestOtherNode();

                // 创建切换按钮
                var buttonPanel = new System.Windows.Controls.StackPanel
                {
                    Orientation = System.Windows.Controls.Orientation.Horizontal,
                    Margin = new Thickness(10)
                };

                var btnDataFilter = new System.Windows.Controls.Button
                {
                    Content = "显示 DataFilter",
                    Margin = new Thickness(5, 5, 5, 5),
                    Padding = new Thickness(10, 5, 10, 5)
                };
                btnDataFilter.Click += (s, e) => panelManager.SetNode(dataFilterNode);

                var btnOther = new System.Windows.Controls.Button
                {
                    Content = "显示其他节点",
                    Margin = new Thickness(5, 5, 5, 5),
                    Padding = new Thickness(10, 5, 10, 5)
                };
                btnOther.Click += (s, e) => panelManager.SetNode(otherNode);

                var btnClear = new System.Windows.Controls.Button
                {
                    Content = "清空",
                    Margin = new Thickness(5, 5, 5, 5),
                    Padding = new Thickness(10, 5, 10, 5)
                };
                btnClear.Click += (s, e) => panelManager.SetNode(null!);

                buttonPanel.Children.Add(btnDataFilter);
                buttonPanel.Children.Add(btnOther);
                buttonPanel.Children.Add(btnClear);

                // 创建主布局
                var mainPanel = new System.Windows.Controls.DockPanel();
                System.Windows.Controls.DockPanel.SetDock(buttonPanel, System.Windows.Controls.Dock.Top);
                mainPanel.Children.Add(buttonPanel);
                mainPanel.Children.Add(container);

                testWindow.Content = mainPanel;

                // 默认显示 DataFilter
                panelManager.SetNode(dataFilterNode);

                // 显示窗口
                testWindow.Show();

                Console.WriteLine("属性面板管理器测试窗口已打开");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建测试窗口失败: {ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 创建其他类型的测试节点
        /// </summary>
        private static ModuleNodeViewModel CreateTestOtherNode()
        {
            var module = new ModuleModel
            {
                Type = ModuleType.FileInput,
                Name = "测试文件输入",
                Description = "用于测试动态属性面板的文件输入节点",
                IsEnabled = true,
                IsVisible = true
            };

            var nodeViewModel = new ModuleNodeViewModel
            {
                Module = module,
                Title = "测试文件输入",
                Location = new Point(200, 200)
            };

            return nodeViewModel;
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== 现代化属性面板测试 ===");

            try
            {
                // 测试1：单独的现代化面板
                Console.WriteLine("1. 测试现代化属性面板...");
                ShowTestWindow();

                // 测试2：属性面板管理器
                Console.WriteLine("2. 测试属性面板管理器...");
                TestPropertyPanelManager();

                Console.WriteLine("所有测试窗口已创建完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                MessageBox.Show($"测试失败: {ex.Message}", "测试错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}

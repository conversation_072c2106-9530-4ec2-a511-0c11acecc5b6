using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 布尔值到GridLength转换器
    /// </summary>
    public class BooleanToGridLengthConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isVisible)
            {
                return isVisible ? new GridLength(300) : new GridLength(0);
            }
            return new GridLength(0);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Media;

namespace ProjectDigitizer.Studio.Models
{
    /// <summary>
    /// 模板分类
    /// </summary>
    public class TemplateCategory : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private Color _color = Colors.Gray;

        /// <summary>
        /// 分类名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 分类颜色
        /// </summary>
        public Color Color
        {
            get => _color;
            set => SetProperty(ref _color, value);
        }

        /// <summary>
        /// 分类中的模板项
        /// </summary>
        public ObservableCollection<TemplateItem> Items { get; } = new ObservableCollection<TemplateItem>();

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}

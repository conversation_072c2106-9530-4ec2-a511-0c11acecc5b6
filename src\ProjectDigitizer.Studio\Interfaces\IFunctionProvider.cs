using System;
using System.Collections.Generic;
using System.Linq;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Interfaces
{
    /// <summary>
    /// 函数参数定义
    /// </summary>
    public class ParameterDefinition
    {
        /// <summary>参数名称</summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>参数显示名称</summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>参数类型</summary>
        public FieldDataType DataType { get; set; } = FieldDataType.Any;

        /// <summary>是否必需</summary>
        public bool IsRequired { get; set; } = true;

        /// <summary>默认值</summary>
        public object? DefaultValue { get; set; }

        /// <summary>参数描述</summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>最小值（数值类型）</summary>
        public double? MinValue { get; set; }

        /// <summary>最大值（数值类型）</summary>
        public double? MaxValue { get; set; }

        /// <summary>允许的值列表（枚举类型）</summary>
        public List<object>? AllowedValues { get; set; }
    }

    /// <summary>
    /// 函数执行结果
    /// </summary>
    public class FunctionResult
    {
        /// <summary>是否执行成功</summary>
        public bool IsSuccess { get; set; }

        /// <summary>执行结果值</summary>
        public object? Value { get; set; }

        /// <summary>结果数据类型</summary>
        public FieldDataType ResultType { get; set; } = FieldDataType.Any;

        /// <summary>错误消息</summary>
        public string? ErrorMessage { get; set; }

        /// <summary>警告消息</summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>执行时间（毫秒）</summary>
        public long ExecutionTimeMs { get; set; }

        /// <summary>创建成功结果</summary>
        public static FunctionResult Success(object? value, FieldDataType resultType = FieldDataType.Any)
        {
            return new FunctionResult
            {
                IsSuccess = true,
                Value = value,
                ResultType = resultType
            };
        }

        /// <summary>创建失败结果</summary>
        public static FunctionResult Error(string errorMessage)
        {
            return new FunctionResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 函数提供者接口
    /// </summary>
    public interface IFunctionProvider
    {
        /// <summary>函数名称</summary>
        string Name { get; }

        /// <summary>函数显示名称</summary>
        string DisplayName { get; }

        /// <summary>函数分类</summary>
        string Category { get; }

        /// <summary>函数描述</summary>
        string Description { get; }

        /// <summary>函数类型</summary>
        FunctionType Type { get; }

        /// <summary>是否支持可变参数</summary>
        bool SupportsVariableParameters { get; }

        /// <summary>获取参数定义列表</summary>
        List<ParameterDefinition> GetParameters();

        /// <summary>执行函数</summary>
        /// <param name="parameters">参数字典</param>
        /// <returns>执行结果</returns>
        FunctionResult Execute(Dictionary<string, object> parameters);

        /// <summary>验证参数</summary>
        /// <param name="parameters">参数字典</param>
        /// <returns>验证结果</returns>
        ValidationResult ValidateParameters(Dictionary<string, object> parameters);

        /// <summary>获取函数使用示例</summary>
        /// <returns>示例列表</returns>
        List<string> GetExamples();

        /// <summary>获取函数语法帮助</summary>
        /// <returns>语法帮助文本</returns>
        string GetSyntaxHelp();
    }

    /// <summary>
    /// 函数提供者基类
    /// </summary>
    public abstract class FunctionProviderBase : IFunctionProvider
    {
        public abstract string Name { get; }
        public abstract string DisplayName { get; }
        public abstract string Category { get; }
        public abstract string Description { get; }
        public abstract FunctionType Type { get; }
        public virtual bool SupportsVariableParameters => false;

        public abstract List<ParameterDefinition> GetParameters();
        public abstract FunctionResult Execute(Dictionary<string, object> parameters);

        public virtual ValidationResult ValidateParameters(Dictionary<string, object> parameters)
        {
            var result = new ValidationResult();
            var parameterDefs = GetParameters();

            // 检查必需参数
            foreach (var paramDef in parameterDefs.Where(p => p.IsRequired))
            {
                if (!parameters.ContainsKey(paramDef.Name) || parameters[paramDef.Name] == null)
                {
                    result.AddError($"缺少必需参数: {paramDef.DisplayName}");
                }
            }

            // 检查参数类型和范围
            foreach (var param in parameters)
            {
                var paramDef = parameterDefs.FirstOrDefault(p => p.Name == param.Key);
                if (paramDef != null)
                {
                    var validation = ValidateParameterValue(param.Value, paramDef);
                    result.Merge(validation);
                }
            }

            return result;
        }

        public virtual List<string> GetExamples()
        {
            return new List<string>();
        }

        public virtual string GetSyntaxHelp()
        {
            var parameters = GetParameters();
            var paramStrings = parameters.Select(p =>
                p.IsRequired ? p.Name : $"[{p.Name}]");
            return $"{Name}({string.Join(", ", paramStrings)})";
        }

        /// <summary>验证单个参数值</summary>
        protected virtual ValidationResult ValidateParameterValue(object? value, ParameterDefinition paramDef)
        {
            var result = new ValidationResult();

            if (value == null)
            {
                if (paramDef.IsRequired)
                {
                    result.AddError($"参数 {paramDef.DisplayName} 不能为空");
                }
                return result;
            }

            // 数值范围检查
            if (paramDef.DataType == FieldDataType.Number && value is IComparable comparableValue)
            {
                if (paramDef.MinValue.HasValue &&
                    comparableValue.CompareTo(paramDef.MinValue.Value) < 0)
                {
                    result.AddError($"参数 {paramDef.DisplayName} 不能小于 {paramDef.MinValue}");
                }

                if (paramDef.MaxValue.HasValue &&
                    comparableValue.CompareTo(paramDef.MaxValue.Value) > 0)
                {
                    result.AddError($"参数 {paramDef.DisplayName} 不能大于 {paramDef.MaxValue}");
                }
            }

            // 允许值检查
            if (paramDef.AllowedValues?.Any() == true &&
                !paramDef.AllowedValues.Contains(value))
            {
                result.AddError($"参数 {paramDef.DisplayName} 的值不在允许范围内");
            }

            return result;
        }

        /// <summary>安全转换参数值</summary>
        protected T? GetParameterValue<T>(Dictionary<string, object> parameters, string paramName, T? defaultValue = default)
        {
            if (!parameters.TryGetValue(paramName, out var value) || value == null)
            {
                return defaultValue;
            }

            try
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>获取数组参数</summary>
        protected List<T> GetArrayParameter<T>(Dictionary<string, object> parameters, string paramName)
        {
            if (!parameters.TryGetValue(paramName, out var value))
            {
                return new List<T>();
            }

            if (value is IEnumerable<T> typedArray)
            {
                return typedArray.ToList();
            }

            if (value is System.Collections.IEnumerable enumerable)
            {
                var result = new List<T>();
                foreach (var item in enumerable)
                {
                    try
                    {
                        result.Add((T)Convert.ChangeType(item, typeof(T)));
                    }
                    catch
                    {
                        // 忽略无法转换的项
                    }
                }
                return result;
            }

            return new List<T>();
        }
    }
}

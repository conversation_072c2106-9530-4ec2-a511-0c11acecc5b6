using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProjectDigitizer.Studio.Models
{
    /// <summary>
    /// 节点属性配置架构
    /// </summary>
    public class NodePropertySchema
    {
        public string NodeType { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, PropertyDefinition> Properties { get; set; } = new();
    }

    /// <summary>
    /// 单个属性的定义
    /// </summary>
    public class PropertyDefinition
    {
        public string Name { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public PropertyType Type { get; set; }
        public string UiWidget { get; set; } = "text";
        public object? DefaultValue { get; set; }
        public bool Required { get; set; }

        // 数据来源标识（基于开发文档7.2节）
        public DataSourceType DataSource { get; set; } = DataSourceType.Manual;

        // 数值类型的约束
        public double? Minimum { get; set; }
        public double? Maximum { get; set; }

        // 选择类型的选项
        public List<SelectOption> Options { get; set; } = new();

        // 条件显示规则
        public string? ShowWhen { get; set; }

        // 验证规则
        public List<ValidationRule> ValidationRules { get; set; } = new();

        // 属性分组
        public string? Group { get; set; }

        // 排序权重
        public int Order { get; set; } = 0;
    }

    /// <summary>
    /// 数据来源类型（基于开发文档7.2节）
    /// </summary>
    public enum DataSourceType
    {
        ProjectInfo,        // 项目信息关联 - 紫色
        CADInfo,           // CAD信息关联 - 橙色
        Manual,            // 手动输入参数 - 绿色
        Fixed,             // 固定默认值 - 灰色
        Formula,           // 公式关联结果 - 橙色
        MaterialLibrary,   // 材料库（字典） - 蓝色
        StandardLibrary,   // 规范与图库 - 青色
        Special,           // 特殊关联 - 紫色
        Error              // 错误信息 - 红色
    }

    /// <summary>
    /// 属性数据类型
    /// </summary>
    public enum PropertyType
    {
        String,
        Number,
        Boolean,
        Select,
        MultiSelect,
        File,
        Directory,
        Date,
        Time,
        DateTime
    }

    /// <summary>
    /// 选择选项
    /// </summary>
    public class SelectOption
    {
        public string Value { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    /// <summary>
    /// 验证规则
    /// </summary>
    public class ValidationRule
    {
        public string Type { get; set; } = string.Empty; // "required", "pattern", "range", etc.
        public string? Pattern { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 节点属性值容器
    /// </summary>
    public class NodePropertyValues : INotifyPropertyChanged
    {
        private readonly Dictionary<string, object?> _values = new();

        public object? GetValue(string propertyName)
        {
            return _values.TryGetValue(propertyName, out var value) ? value : null;
        }

        public T? GetValue<T>(string propertyName)
        {
            var value = GetValue(propertyName);
            if (value is T typedValue)
                return typedValue;
            return default(T);
        }

        public void SetValue(string propertyName, object? value)
        {
            if (!_values.TryGetValue(propertyName, out var currentValue) ||
                !Equals(currentValue, value))
            {
                _values[propertyName] = value;
                OnPropertyChanged(propertyName);
            }
        }

        public Dictionary<string, object?> GetAllValues()
        {
            return new Dictionary<string, object?>(_values);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
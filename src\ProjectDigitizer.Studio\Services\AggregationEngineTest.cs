using System;
using System.Collections.Generic;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 统计引擎测试类
    /// 用于验证统计功能的正确性
    /// </summary>
    public static class AggregationEngineTest
    {
        /// <summary>
        /// 运行基础统计测试
        /// </summary>
        public static void RunBasicTests()
        {
            var engine = new AggregationEngine();
            
            Console.WriteLine("=== 统计引擎测试 ===");
            
            // 创建测试数据
            var testData = CreateTestData();
            Console.WriteLine($"测试数据: {testData.Count} 条记录");
            
            // 测试各种统计函数
            TestCountFunction(engine, testData);
            TestSumFunction(engine, testData);
            TestAverageFunction(engine, testData);
            TestMinMaxFunctions(engine, testData);
            TestMultipleAggregations(engine, testData);
        }

        /// <summary>
        /// 测试计数函数
        /// </summary>
        private static void TestCountFunction(AggregationEngine engine, List<Dictionary<string, object?>> data)
        {
            Console.WriteLine("\n--- 测试计数函数 ---");
            
            // 测试总计数
            var countConfig = AggregationEngine.CreateConfig(AggregationFunction.Count);
            var countResult = engine.ExecuteAggregation(data, countConfig);
            
            Console.WriteLine($"总记录数: {countResult.FormattedValue} (期望: {data.Count})");
            Console.WriteLine($"执行时间: {countResult.ExecutionTime.TotalMilliseconds:F2}ms");
            
            // 测试字段计数
            var fieldCountConfig = AggregationEngine.CreateConfig(AggregationFunction.Count, "score");
            var fieldCountResult = engine.ExecuteAggregation(data, fieldCountConfig);
            
            Console.WriteLine($"score字段计数: {fieldCountResult.FormattedValue}");
            Console.WriteLine($"有效值数量: {fieldCountResult.ValidValueCount}");
        }

        /// <summary>
        /// 测试求和函数
        /// </summary>
        private static void TestSumFunction(AggregationEngine engine, List<Dictionary<string, object?>> data)
        {
            Console.WriteLine("\n--- 测试求和函数 ---");
            
            var sumConfig = AggregationEngine.CreateConfig(AggregationFunction.Sum, "score");
            var sumResult = engine.ExecuteAggregation(data, sumConfig);
            
            Console.WriteLine($"score字段求和: {sumResult.FormattedValue}");
            Console.WriteLine($"参与计算的记录数: {sumResult.ValidValueCount}");
            Console.WriteLine($"执行状态: {(sumResult.IsSuccess ? "成功" : "失败")}");
            
            if (!sumResult.IsSuccess)
            {
                Console.WriteLine($"错误信息: {sumResult.ErrorMessage}");
            }
        }

        /// <summary>
        /// 测试平均值函数
        /// </summary>
        private static void TestAverageFunction(AggregationEngine engine, List<Dictionary<string, object?>> data)
        {
            Console.WriteLine("\n--- 测试平均值函数 ---");
            
            var avgConfig = AggregationEngine.CreateConfig(AggregationFunction.Average, "score", 2);
            var avgResult = engine.ExecuteAggregation(data, avgConfig);
            
            Console.WriteLine($"score字段平均值: {avgResult.FormattedValue}");
            Console.WriteLine($"参与计算的记录数: {avgResult.ValidValueCount}");
        }

        /// <summary>
        /// 测试最小值和最大值函数
        /// </summary>
        private static void TestMinMaxFunctions(AggregationEngine engine, List<Dictionary<string, object?>> data)
        {
            Console.WriteLine("\n--- 测试最小值和最大值函数 ---");
            
            // 测试数值字段
            var minConfig = AggregationEngine.CreateConfig(AggregationFunction.Min, "score");
            var minResult = engine.ExecuteAggregation(data, minConfig);
            
            var maxConfig = AggregationEngine.CreateConfig(AggregationFunction.Max, "score");
            var maxResult = engine.ExecuteAggregation(data, maxConfig);
            
            Console.WriteLine($"score字段最小值: {minResult.FormattedValue}");
            Console.WriteLine($"score字段最大值: {maxResult.FormattedValue}");
            
            // 测试字符串字段
            var minNameConfig = AggregationEngine.CreateConfig(AggregationFunction.Min, "name");
            var minNameResult = engine.ExecuteAggregation(data, minNameConfig);
            
            var maxNameConfig = AggregationEngine.CreateConfig(AggregationFunction.Max, "name");
            var maxNameResult = engine.ExecuteAggregation(data, maxNameConfig);
            
            Console.WriteLine($"name字段最小值: {minNameResult.FormattedValue}");
            Console.WriteLine($"name字段最大值: {maxNameResult.FormattedValue}");
        }

        /// <summary>
        /// 测试多重统计
        /// </summary>
        private static void TestMultipleAggregations(AggregationEngine engine, List<Dictionary<string, object?>> data)
        {
            Console.WriteLine("\n--- 测试多重统计 ---");
            
            var configs = new List<AggregationConfig>
            {
                AggregationEngine.CreateConfig(AggregationFunction.Count),
                AggregationEngine.CreateConfig(AggregationFunction.Sum, "score"),
                AggregationEngine.CreateConfig(AggregationFunction.Average, "score", 2),
                AggregationEngine.CreateConfig(AggregationFunction.Min, "score"),
                AggregationEngine.CreateConfig(AggregationFunction.Max, "score")
            };
            
            var resultSet = engine.ExecuteMultipleAggregations(data, configs);
            
            Console.WriteLine($"统计摘要: {resultSet.GetSummary()}");
            Console.WriteLine($"总执行时间: {resultSet.TotalExecutionTime.TotalMilliseconds:F2}ms");
            Console.WriteLine($"所有统计是否成功: {resultSet.IsAllSuccess}");
            
            Console.WriteLine("\n详细结果:");
            foreach (var result in resultSet.Results)
            {
                Console.WriteLine($"  {result.Description}");
            }
        }

        /// <summary>
        /// 创建测试数据
        /// </summary>
        private static List<Dictionary<string, object?>> CreateTestData()
        {
            return new List<Dictionary<string, object?>>
            {
                new() { ["id"] = 1, ["name"] = "张三", ["score"] = 95, ["class"] = "A", ["age"] = 20 },
                new() { ["id"] = 2, ["name"] = "李四", ["score"] = 87, ["class"] = "B", ["age"] = 19 },
                new() { ["id"] = 3, ["name"] = "王五", ["score"] = 92, ["class"] = "A", ["age"] = 21 },
                new() { ["id"] = 4, ["name"] = "赵六", ["score"] = 78, ["class"] = "C", ["age"] = 18 },
                new() { ["id"] = 5, ["name"] = "钱七", ["score"] = 88, ["class"] = "B", ["age"] = 20 },
                new() { ["id"] = 6, ["name"] = "孙八", ["score"] = null, ["class"] = "A", ["age"] = 19 }, // null值测试
                new() { ["id"] = 7, ["name"] = "周九", ["score"] = 96, ["class"] = "A", ["age"] = 22 },
                new() { ["id"] = 8, ["name"] = "吴十", ["score"] = 82, ["class"] = "C", ["age"] = 20 }
            };
        }

        /// <summary>
        /// 运行性能测试
        /// </summary>
        public static void RunPerformanceTest()
        {
            Console.WriteLine("\n=== 统计性能测试 ===");
            
            var engine = new AggregationEngine();
            var largeData = GenerateLargeTestData(50000);
            
            Console.WriteLine($"生成测试数据: {largeData.Count} 条记录");
            
            var configs = AggregationEngine.CreateCommonConfigs("score");
            
            var startTime = DateTime.Now;
            var resultSet = engine.ExecuteMultipleAggregations(largeData, configs);
            var endTime = DateTime.Now;
            
            var totalTime = endTime - startTime;
            
            Console.WriteLine($"统计结果: {resultSet.GetSummary()}");
            Console.WriteLine($"总执行时间: {totalTime.TotalMilliseconds:F2}ms");
            Console.WriteLine($"平均每条记录: {totalTime.TotalMilliseconds / largeData.Count:F4}ms");
            Console.WriteLine($"每秒处理记录数: {largeData.Count / totalTime.TotalSeconds:F0}");
            
            // 显示统计结果
            foreach (var result in resultSet.Results)
            {
                if (result.IsSuccess)
                {
                    Console.WriteLine($"  {GetFunctionName(result.Function)}: {result.FormattedValue}");
                }
            }
        }

        /// <summary>
        /// 生成大量测试数据
        /// </summary>
        private static List<Dictionary<string, object?>> GenerateLargeTestData(int count)
        {
            var random = new Random();
            var data = new List<Dictionary<string, object?>>();
            var names = new[] { "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十" };
            var classes = new[] { "A", "B", "C", "D" };
            
            for (int i = 0; i < count; i++)
            {
                data.Add(new Dictionary<string, object?>
                {
                    ["id"] = i + 1,
                    ["name"] = names[random.Next(names.Length)],
                    ["score"] = random.Next(0, 101),
                    ["class"] = classes[random.Next(classes.Length)],
                    ["age"] = random.Next(16, 26)
                });
            }
            
            return data;
        }

        /// <summary>
        /// 获取函数名称
        /// </summary>
        private static string GetFunctionName(AggregationFunction function)
        {
            return function switch
            {
                AggregationFunction.Count => "计数",
                AggregationFunction.Sum => "求和",
                AggregationFunction.Average => "平均值",
                AggregationFunction.Min => "最小值",
                AggregationFunction.Max => "最大值",
                _ => "未知"
            };
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                RunBasicTests();
                RunPerformanceTest();
                Console.WriteLine("\n=== 统计测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n统计测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
    }
}

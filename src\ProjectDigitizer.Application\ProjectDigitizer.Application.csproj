<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="MediatR" Version="12.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProjectDigitizer.Core\ProjectDigitizer.Core.csproj" />
    <ProjectReference Include="..\ProjectDigitizer.Infrastructure\ProjectDigitizer.Infrastructure.csproj" />
    <ProjectReference Include="..\ProjectDigitizer.Shared\ProjectDigitizer.Shared.csproj" />
  </ItemGroup>

</Project>

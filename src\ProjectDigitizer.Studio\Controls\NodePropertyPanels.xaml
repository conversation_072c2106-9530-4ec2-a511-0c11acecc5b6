<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
                    xmlns:models="clr-namespace:ProjectDigitizer.Studio.Models">

    <!-- 输入节点属性面板 -->
    <DataTemplate x:Key="InputNodePropertyPanel"
                  DataType="{x:Type viewmodels:ModuleNodeViewModel}">
        <ScrollViewer VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled">
            <StackPanel Margin="0">
                <!-- 基本信息组 -->
                <GroupBox Header="基本信息"
                          Margin="0,0,0,20"
                          Padding="12">
                    <StackPanel Margin="0">
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="85"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="节点名称:"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"/>
                            <TextBox Grid.Column="1"
                                     Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="8,0,0,0"
                                     Padding="8,6"/>
                        </Grid>

                        <Grid Margin="0,0,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="85"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="节点类型:"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"/>
                            <TextBlock Grid.Column="1"
                                       Text="{Binding NodeTypeMetadata.Name}"
                                       VerticalAlignment="Center"
                                       FontWeight="SemiBold"
                                       Margin="8,0,0,0"
                                       Foreground="{DynamicResource MaterialDesignPrimary}"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- 数据源配置组 -->
                <GroupBox Header="数据源配置"
                          Margin="0,0,0,20"
                          Padding="12">
                    <StackPanel Margin="0">
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="85"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="数据源:"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"/>
                            <ComboBox Grid.Column="1"
                                      Margin="8,0,0,0"
                                      Padding="8,6">
                                <ComboBoxItem Content="文件"
                                              IsSelected="True"/>
                                <ComboBoxItem Content="数据库"/>
                                <ComboBoxItem Content="API接口"/>
                                <ComboBoxItem Content="CAD图纸"/>
                            </ComboBox>
                        </Grid>

                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="85"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="文件路径:"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"/>
                            <TextBox Grid.Column="1"
                                     Margin="8,0,8,0"
                                     Padding="8,6"/>
                            <Button Grid.Column="2"
                                    Content="浏览..."
                                    Width="65"
                                    Padding="8,6"/>
                        </Grid>

                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="85"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="文件格式:"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"/>
                            <ComboBox Grid.Column="1"
                                      Margin="8,0,0,0"
                                      Padding="8,6">
                                <ComboBoxItem Content="自动检测"
                                              IsSelected="True"/>
                                <ComboBoxItem Content="Excel (.xlsx)"/>
                                <ComboBoxItem Content="CSV (.csv)"/>
                                <ComboBoxItem Content="JSON (.json)"/>
                                <ComboBoxItem Content="XML (.xml)"/>
                            </ComboBox>
                        </Grid>

                        <CheckBox Content="包含标题行"
                                  IsChecked="True"
                                  Margin="85,0,0,0"
                                  Padding="8,6"/>
                    </StackPanel>
                </GroupBox>

                <!-- 高级选项组 -->
                <GroupBox Header="高级选项"
                          Padding="12">
                    <StackPanel Margin="0">
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="85"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="编码格式:"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"/>
                            <ComboBox Grid.Column="1"
                                      Margin="8,0,0,0"
                                      Padding="8,6">
                                <ComboBoxItem Content="UTF-8"
                                              IsSelected="True"/>
                                <ComboBoxItem Content="GBK"/>
                                <ComboBoxItem Content="ASCII"/>
                            </ComboBox>
                        </Grid>

                        <CheckBox Content="启用数据缓存"
                                  IsChecked="True"
                                  Margin="85,0,0,8"
                                  Padding="8,6"/>
                        <CheckBox Content="自动刷新数据"
                                  Margin="85,0,0,0"
                                  Padding="8,6"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </DataTemplate>

    <!-- 转换节点属性面板 -->
    <DataTemplate x:Key="TransformNodePropertyPanel"
                  DataType="{x:Type viewmodels:ModuleNodeViewModel}">
        <ScrollViewer VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled">
            <StackPanel Margin="0">
                <!-- 基本信息组 -->
                <GroupBox Header="基本信息"
                          Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="节点名称:"
                                       VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1"
                                     Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>

                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="节点类型:"
                                       VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                       Text="{Binding NodeTypeMetadata.Name}"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- 转换配置组 -->
                <GroupBox Header="转换配置"
                          Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="转换类型:"
                                       VerticalAlignment="Center"/>
                            <ComboBox Grid.Column="1">
                                <ComboBoxItem Content="数据过滤"
                                              IsSelected="True"/>
                                <ComboBoxItem Content="字段映射"/>
                                <ComboBoxItem Content="数据计算"/>
                                <ComboBoxItem Content="格式转换"/>
                            </ComboBox>
                        </Grid>

                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="表达式:"
                                       VerticalAlignment="Top"
                                       Margin="0,4,0,0"/>
                            <TextBox Grid.Column="1"
                                     Height="60"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>
                        </Grid>

                        <CheckBox Content="启用数据验证"
                                  IsChecked="True"/>
                    </StackPanel>
                </GroupBox>

                <!-- 字段映射组 -->
                <GroupBox Header="字段映射">
                    <StackPanel Margin="8">
                        <Grid Height="120">
                            <DataGrid AutoGenerateColumns="False"
                                      CanUserAddRows="True">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="源字段"
                                                        Width="*"/>
                                    <DataGridTextColumn Header="目标字段"
                                                        Width="*"/>
                                    <DataGridTextColumn Header="转换规则"
                                                        Width="*"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>

                        <StackPanel Orientation="Horizontal"
                                    Margin="0,8,0,0">
                            <Button Content="添加映射"
                                    Width="80"
                                    Margin="0,0,8,0"/>
                            <Button Content="删除映射"
                                    Width="80"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </DataTemplate>

    <!-- 输出节点属性面板 -->
    <DataTemplate x:Key="OutputNodePropertyPanel"
                  DataType="{x:Type viewmodels:ModuleNodeViewModel}">
        <ScrollViewer VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled">
            <StackPanel Margin="0">
                <!-- 基本信息组 -->
                <GroupBox Header="基本信息"
                          Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="节点名称:"
                                       VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1"
                                     Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>

                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="节点类型:"
                                       VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                       Text="{Binding NodeTypeMetadata.Name}"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- 输出配置组 -->
                <GroupBox Header="输出配置"
                          Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="输出类型:"
                                       VerticalAlignment="Center"/>
                            <ComboBox Grid.Column="1">
                                <ComboBoxItem Content="文件输出"
                                              IsSelected="True"/>
                                <ComboBoxItem Content="数据库"/>
                                <ComboBoxItem Content="邮件发送"/>
                                <ComboBoxItem Content="报表生成"/>
                            </ComboBox>
                        </Grid>

                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="输出路径:"
                                       VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1"
                                     Margin="0,0,8,0"/>
                            <Button Grid.Column="2"
                                    Content="浏览..."
                                    Width="60"/>
                        </Grid>

                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="输出格式:"
                                       VerticalAlignment="Center"/>
                            <ComboBox Grid.Column="1">
                                <ComboBoxItem Content="Excel (.xlsx)"
                                              IsSelected="True"/>
                                <ComboBoxItem Content="CSV (.csv)"/>
                                <ComboBoxItem Content="PDF (.pdf)"/>
                                <ComboBoxItem Content="Word (.docx)"/>
                            </ComboBox>
                        </Grid>

                        <CheckBox Content="覆盖已存在文件"
                                  IsChecked="False"/>
                    </StackPanel>
                </GroupBox>

                <!-- 模板配置组 -->
                <GroupBox Header="模板配置">
                    <StackPanel Margin="8">
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="模板文件:"
                                       VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1"
                                     Margin="0,0,8,0"/>
                            <Button Grid.Column="2"
                                    Content="选择..."
                                    Width="60"/>
                        </Grid>

                        <CheckBox Content="使用默认模板"
                                  IsChecked="True"/>
                        <CheckBox Content="包含数据统计"
                                  Margin="0,4,0,0"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </DataTemplate>

    <!-- 控制节点属性面板 -->
    <DataTemplate x:Key="ControlNodePropertyPanel"
                  DataType="{x:Type viewmodels:ModuleNodeViewModel}">
        <ScrollViewer VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled">
            <StackPanel Margin="0">
                <!-- 基本信息组 -->
                <GroupBox Header="基本信息"
                          Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="节点名称:"
                                       VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1"
                                     Text="{Binding Title, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>

                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="节点类型:"
                                       VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                       Text="{Binding NodeTypeMetadata.Name}"
                                       VerticalAlignment="Center"
                                       FontWeight="Medium"/>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- 控制配置组 -->
                <GroupBox Header="控制配置"
                          Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="控制类型:"
                                       VerticalAlignment="Center"/>
                            <ComboBox Grid.Column="1">
                                <ComboBoxItem Content="条件判断"
                                              IsSelected="True"/>
                                <ComboBoxItem Content="循环处理"/>
                                <ComboBoxItem Content="错误处理"/>
                                <ComboBoxItem Content="流程控制"/>
                            </ComboBox>
                        </Grid>

                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="条件表达式:"
                                       VerticalAlignment="Top"
                                       Margin="0,4,0,0"/>
                            <TextBox Grid.Column="1"
                                     Height="60"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"/>
                        </Grid>

                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="最大迭代:"
                                       VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1"
                                     Text="100"/>
                        </Grid>

                        <CheckBox Content="错误时继续执行"
                                  IsChecked="False"/>
                    </StackPanel>
                </GroupBox>

                <!-- 超时配置组 -->
                <GroupBox Header="超时配置">
                    <StackPanel Margin="8">
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="超时时间:"
                                       VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1"
                                     Text="30"
                                     Margin="0,0,8,0"/>
                            <TextBlock Grid.Column="2"
                                       Text="秒"
                                       VerticalAlignment="Center"/>
                        </Grid>

                        <CheckBox Content="启用超时检测"
                                  IsChecked="True"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </DataTemplate>

</ResourceDictionary>

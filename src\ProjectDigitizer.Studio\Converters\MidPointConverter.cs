using System;
using System.Globalization;
using System.Windows.Data;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 计算连接线中点X坐标的转换器
    /// </summary>
    public class MidPointXConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length == 2 && values[0] is double x1 && values[1] is double x2)
            {
                // 计算中点X坐标，并偏移8像素使标识居中
                return (x1 + x2) / 2 - 8;
            }
            return 0.0;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 计算连接线中点Y坐标的转换器
    /// </summary>
    public class MidPointYConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length == 2 && values[0] is double y1 && values[1] is double y2)
            {
                // 计算中点Y坐标，并偏移8像素使标识居中
                return (y1 + y2) / 2 - 8;
            }
            return 0.0;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}

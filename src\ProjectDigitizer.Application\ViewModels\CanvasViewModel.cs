using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using ProjectDigitizer.Core.Models;
using ProjectDigitizer.Core.Enums;
using ProjectDigitizer.Application.Commands;

namespace ProjectDigitizer.Application.ViewModels;

/// <summary>
/// 画布视图模型 - 包含性能优化功能
/// </summary>
public class CanvasViewModel : ViewModelBase
{
    private ModuleNodeViewModel? _selectedNode;
    private bool _isPropertyPanelVisible;
    private Point _viewportCenter = new Point(400, 300); // 默认视口中心
    private double _viewportZoom = 1.0; // 默认缩放级别
    private Size _actualCanvasSize = new Size(1200, 800); // 实际画布尺寸
    private Rect _actualViewport = new Rect(0, 0, 1200, 800); // 实际可见视口

    public CanvasViewModel()
    {
        Nodes = new ObservableCollection<ModuleNodeViewModel>();
        Connections = new ObservableCollection<ConnectionViewModel>();
        SelectedItems = new ObservableCollection<object>();
        TemplateCategories = new ObservableCollection<TemplateCategory>();
        
        // 初始化命令
        AddModuleCommand = new DelegateCommand<TemplateItem>(ExecuteAddModule, CanExecuteAddModule);
        CreateConnectionCommand = new DelegateCommand<object>(ExecuteCreateConnection);
        DisconnectConnectorCommand = new DelegateCommand<ConnectorViewModel>(ExecuteDisconnectConnector);
        RemoveConnectionCommand = new DelegateCommand<ConnectionViewModel>(ExecuteRemoveConnection);
        
        // 初始化待连接
        PendingConnection = new PendingConnectionViewModel();
        
        // 初始化模板分类
        InitializeTemplateCategories();
    }

    #region Properties

    /// <summary>
    /// 节点集合
    /// </summary>
    public ObservableCollection<ModuleNodeViewModel> Nodes { get; }

    /// <summary>
    /// 连接线集合
    /// </summary>
    public ObservableCollection<ConnectionViewModel> Connections { get; }

    /// <summary>
    /// 选中的节点集合（支持多选）
    /// </summary>
    public ObservableCollection<object> SelectedItems { get; }

    /// <summary>
    /// 模板分类集合
    /// </summary>
    public ObservableCollection<TemplateCategory> TemplateCategories { get; }

    /// <summary>
    /// 属性面板是否可见
    /// </summary>
    public bool IsPropertyPanelVisible
    {
        get => _isPropertyPanelVisible;
        set => SetProperty(ref _isPropertyPanelVisible, value);
    }

    /// <summary>
    /// 选中的节点（单选）
    /// </summary>
    public ModuleNodeViewModel? SelectedNode
    {
        get => _selectedNode;
        set => SetProperty(ref _selectedNode, value);
    }

    /// <summary>
    /// 视口中心位置
    /// </summary>
    public Point ViewportCenter
    {
        get => _viewportCenter;
        set => SetProperty(ref _viewportCenter, value);
    }

    /// <summary>
    /// 视口缩放级别
    /// </summary>
    public double ViewportZoom
    {
        get => _viewportZoom;
        set => SetProperty(ref _viewportZoom, value);
    }

    /// <summary>
    /// 实际画布尺寸
    /// </summary>
    public Size ActualCanvasSize
    {
        get => _actualCanvasSize;
        set => SetProperty(ref _actualCanvasSize, value);
    }

    /// <summary>
    /// 实际可见视口
    /// </summary>
    public Rect ActualViewport
    {
        get => _actualViewport;
        set => SetProperty(ref _actualViewport, value);
    }

    /// <summary>
    /// 待连接视图模型
    /// </summary>
    public PendingConnectionViewModel PendingConnection { get; }

    #endregion

    #region Commands

    /// <summary>
    /// 添加模块命令
    /// </summary>
    public ICommand AddModuleCommand { get; }

    /// <summary>
    /// 创建连接命令
    /// </summary>
    public ICommand CreateConnectionCommand { get; }

    /// <summary>
    /// 断开连接器命令
    /// </summary>
    public ICommand DisconnectConnectorCommand { get; }

    /// <summary>
    /// 移除连接命令
    /// </summary>
    public ICommand RemoveConnectionCommand { get; }

    #endregion

    #region Events

    /// <summary>
    /// 适应节点视图请求事件
    /// </summary>
    public event EventHandler? FitToNodesRequested;

    #endregion

    #region Public Methods

    /// <summary>
    /// 添加模块到画布
    /// </summary>
    /// <param name="moduleType">模块类型</param>
    /// <param name="position">位置</param>
    public ModuleNodeViewModel AddModule(ModuleType moduleType, Point position)
    {
        var module = new ModuleModel
        {
            Type = moduleType,
            Name = GetDefaultModuleName(moduleType)
        };

        var nodeViewModel = new ModuleNodeViewModel
        {
            Module = module,
            Location = position,
            Title = module.Name
        };

        Nodes.Add(nodeViewModel);
        return nodeViewModel;
    }

    /// <summary>
    /// 移除节点
    /// </summary>
    /// <param name="node">要移除的节点</param>
    public void RemoveNode(ModuleNodeViewModel node)
    {
        if (node == null) return;

        // 移除相关连接
        var connectionsToRemove = Connections
            .Where(c => c.SourceNode == node || c.TargetNode == node)
            .ToList();

        foreach (var connection in connectionsToRemove)
        {
            Connections.Remove(connection);
        }

        // 移除节点
        Nodes.Remove(node);
        SelectedItems.Remove(node);

        if (SelectedNode == node)
        {
            SelectedNode = null;
            IsPropertyPanelVisible = false;
        }
    }

    /// <summary>
    /// 清空画布
    /// </summary>
    public void ClearCanvas()
    {
        Connections.Clear();
        Nodes.Clear();
        SelectedItems.Clear();
        SelectedNode = null;
        IsPropertyPanelVisible = false;
    }

    /// <summary>
    /// 选择单个节点
    /// </summary>
    /// <param name="node">要选择的节点</param>
    public void SelectSingleNode(ModuleNodeViewModel node)
    {
        SelectedItems.Clear();
        if (node != null)
        {
            SelectedItems.Add(node);
            SelectedNode = node;
            IsPropertyPanelVisible = true;
        }
        else
        {
            SelectedNode = null;
            IsPropertyPanelVisible = false;
        }
    }

    /// <summary>
    /// 切换节点选择状态
    /// </summary>
    /// <param name="node">节点</param>
    public void ToggleNodeSelection(ModuleNodeViewModel node)
    {
        if (SelectedItems.Contains(node))
        {
            SelectedItems.Remove(node);
        }
        else
        {
            SelectedItems.Add(node);
        }

        // 更新单选节点和属性面板状态
        if (SelectedItems.Count == 1 && SelectedItems[0] is ModuleNodeViewModel singleNode)
        {
            SelectedNode = singleNode;
            IsPropertyPanelVisible = true;
        }
        else
        {
            SelectedNode = null;
            IsPropertyPanelVisible = false;
        }
    }

    /// <summary>
    /// 设置所有模块的启用状态
    /// </summary>
    /// <param name="enabled">是否启用</param>
    public void SetAllModulesEnabled(bool enabled)
    {
        foreach (var node in Nodes)
        {
            if (node.Module != null)
            {
                node.Module.IsEnabled = enabled;
            }
        }
    }

    /// <summary>
    /// 适应所有节点到视图
    /// </summary>
    public void FitToNodes()
    {
        FitToNodesRequested?.Invoke(this, EventArgs.Empty);
    }

    #endregion

    #region Private Methods

    private void InitializeTemplateCategories()
    {
        // 这里会初始化所有模板分类
        // 由于内容较多，后续会通过str-replace-editor添加
    }

    private string GetDefaultModuleName(ModuleType moduleType)
    {
        return moduleType switch
        {
            ModuleType.PipeLine => "平面管线",
            ModuleType.RiserPipe => "立管",
            ModuleType.DataFilter => "数据过滤",
            ModuleType.ExcelExport => "Excel导出",
            _ => moduleType.ToString()
        };
    }

    #endregion

    #region Command Implementations

    private void ExecuteAddModule(TemplateItem? templateItem)
    {
        if (templateItem != null)
        {
            AddModule(templateItem.ModuleType, new Point(100, 100));
        }
    }

    private bool CanExecuteAddModule(TemplateItem? templateItem)
    {
        return templateItem != null;
    }

    private void ExecuteCreateConnection(object? target)
    {
        // 连接创建逻辑
        if (target is ConnectorViewModel targetConnector && PendingConnection.Source != null)
        {
            CreateConnection(PendingConnection.Source, targetConnector);
        }
    }

    private void ExecuteDisconnectConnector(ConnectorViewModel? connector)
    {
        if (connector == null) return;

        var connectionsToRemove = Connections
            .Where(c => c.Source == connector || c.Target == connector)
            .ToList();

        foreach (var connection in connectionsToRemove)
        {
            Connections.Remove(connection);
        }
    }

    private void ExecuteRemoveConnection(ConnectionViewModel? connection)
    {
        if (connection != null)
        {
            Connections.Remove(connection);
        }
    }

    private void CreateConnection(ConnectorViewModel source, ConnectorViewModel target)
    {
        if (source == null || target == null) return;
        if (source == target) return; // 不能连接自己
        if (source.IsInput == target.IsInput) return; // 输入只能连接输出

        var connection = new ConnectionViewModel
        {
            Source = source,
            Target = target,
            SourceNode = source.Owner,
            TargetNode = target.Owner
        };

        Connections.Add(connection);
    }

    #endregion
}

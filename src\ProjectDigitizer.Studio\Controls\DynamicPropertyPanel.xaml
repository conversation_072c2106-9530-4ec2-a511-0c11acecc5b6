<UserControl x:Class="ProjectDigitizer.Studio.Controls.DynamicPropertyPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:selectors="clr-namespace:ProjectDigitizer.Studio.Selectors"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="450">

    <UserControl.Resources>
        <!-- 节点属性数据模板选择器 -->
        <selectors:NodePropertyDataTemplateSelector x:Key="NodePropertyTemplateSelector"
                                                    InputNodeTemplate="{StaticResource InputNodePropertyPanel}"
                                                    TransformNodeTemplate="{StaticResource TransformNodePropertyPanel}"
                                                    OutputNodeTemplate="{StaticResource OutputNodePropertyPanel}"
                                                    ControlNodeTemplate="{StaticResource ControlNodePropertyPanel}"
                                                    DefaultTemplate="{StaticResource TransformNodePropertyPanel}"/>
    </UserControl.Resources>

    <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp8"
                         Margin="8">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <materialDesign:ColorZone Grid.Row="0"
                                      Mode="PrimaryMid"
                                      Padding="20,16">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Settings"
                                             Width="24"
                                             Height="24"
                                             Margin="0,0,12,0"
                                             Foreground="{DynamicResource MaterialDesignDarkForeground}"/>
                    <StackPanel>
                        <TextBlock Text="属性配置"
                                   FontSize="18"
                                   FontWeight="Medium"
                                   Foreground="{DynamicResource MaterialDesignDarkForeground}"/>
                        <TextBlock Text="配置选中模块的参数"
                                   FontSize="12"
                                   Opacity="0.7"
                                   Foreground="{DynamicResource MaterialDesignDarkForeground}"
                                   Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:ColorZone>

            <!-- 内容区域 -->
            <ScrollViewer Grid.Row="1"
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled"
                          Padding="16,20,16,20"
                          materialDesign:ScrollViewerAssist.IsAutoHideEnabled="True">

                <Grid x:Name="PropertyContainer">
                    <!-- 没有选中节点时的提示 -->
                    <StackPanel x:Name="EmptyStatePanel"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Margin="0,40,0,60">
                        <materialDesign:PackIcon Kind="CursorDefault"
                                                 Width="48"
                                                 Height="48"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                 HorizontalAlignment="Center"
                                                 Margin="0,0,0,16"/>
                        <TextBlock x:Name="EmptyStateText"
                                   Text="点击画布中的节点查看属性"
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                   FontSize="14"
                                   TextAlignment="Center"
                                   TextWrapping="Wrap"
                                   MaxWidth="200"/>
                    </StackPanel>

                    <!-- 动态属性面板 -->
                    <ContentPresenter x:Name="PropertyContentPresenter"
                                      ContentTemplateSelector="{StaticResource NodePropertyTemplateSelector}"
                                      Visibility="Collapsed"
                                      Margin="0"/>
                </Grid>

            </ScrollViewer>

            <!-- 操作按钮区域 -->
            <materialDesign:ColorZone Grid.Row="2"
                                      Mode="PrimaryLight"
                                      Padding="20,12">

                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Right">

                    <Button x:Name="ResetButton"
                            Content="重置"
                            Background="Transparent"
                            Foreground="{DynamicResource MaterialDesignBody}"
                            BorderBrush="{DynamicResource MaterialDesignPrimary}"
                            BorderThickness="1"
                            Padding="16,8"
                            Margin="0,0,12,0"
                            Click="ResetButton_Click"/>

                    <Button x:Name="ApplyButton"
                            Content="应用"
                            Background="{DynamicResource MaterialDesignPrimary}"
                            Foreground="{DynamicResource MaterialDesignPrimaryForeground}"
                            BorderThickness="0"
                            Padding="16,8"
                            Click="ApplyButton_Click"/>

                </StackPanel>

            </materialDesign:ColorZone>

        </Grid>

    </materialDesign:Card>

</UserControl> 
using ProjectDigitizer.Core.Models;
using ProjectDigitizer.Core.Enums;

namespace ProjectDigitizer.Core.Interfaces;

/// <summary>
/// 模块服务接口
/// </summary>
public interface IModuleService
{
    /// <summary>
    /// 创建模块
    /// </summary>
    /// <param name="moduleType">模块类型</param>
    /// <param name="name">模块名称</param>
    /// <returns>创建的模块</returns>
    ModuleModel CreateModule(ModuleType moduleType, string? name = null);

    /// <summary>
    /// 获取模块
    /// </summary>
    /// <param name="id">模块ID</param>
    /// <returns>模块实例</returns>
    ModuleModel? GetModule(string id);

    /// <summary>
    /// 更新模块
    /// </summary>
    /// <param name="module">模块实例</param>
    /// <returns>是否更新成功</returns>
    bool UpdateModule(ModuleModel module);

    /// <summary>
    /// 删除模块
    /// </summary>
    /// <param name="id">模块ID</param>
    /// <returns>是否删除成功</returns>
    bool DeleteModule(string id);

    /// <summary>
    /// 获取所有模块
    /// </summary>
    /// <returns>模块列表</returns>
    IEnumerable<ModuleModel> GetAllModules();

    /// <summary>
    /// 根据类型获取模块
    /// </summary>
    /// <param name="moduleType">模块类型</param>
    /// <returns>模块列表</returns>
    IEnumerable<ModuleModel> GetModulesByType(ModuleType moduleType);

    /// <summary>
    /// 验证模块配置
    /// </summary>
    /// <param name="module">模块实例</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateModule(ModuleModel module);
}



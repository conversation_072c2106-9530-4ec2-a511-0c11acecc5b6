using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 表达式解析和执行引擎
    /// 支持基础运算符、逻辑运算符和括号分组
    /// </summary>
    public class ExpressionEngine
    {
        private static readonly Dictionary<string, BinaryOperatorType> BinaryOperators = new()
        {
            { "==", BinaryOperatorType.Equal },
            { "!=", BinaryOperatorType.NotEqual },
            { ">=", BinaryOperatorType.GreaterThanOrEqual },
            { "<=", BinaryOperatorType.LessThanOrEqual },
            { ">", BinaryOperatorType.GreaterThan },
            { "<", BinaryOperatorType.LessThan },
            { "&&", BinaryOperatorType.And },
            { "||", BinaryOperatorType.Or }
        };

        private static readonly Dictionary<string, UnaryOperatorType> UnaryOperators = new()
        {
            { "!", UnaryOperatorType.Not },
            { "not", UnaryOperatorType.Not },
            { "NOT", UnaryOperatorType.Not }
        };

        /// <summary>
        /// 解析表达式字符串
        /// </summary>
        /// <param name="expression">表达式字符串</param>
        /// <param name="caseSensitive">是否区分大小写</param>
        /// <returns>解析结果</returns>
        public ExpressionParseResult ParseExpression(string expression, bool caseSensitive = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(expression))
                {
                    return new ExpressionParseResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "表达式不能为空"
                    };
                }

                var tokens = TokenizeExpression(expression);
                if (tokens.Count == 0)
                {
                    return new ExpressionParseResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "无效的表达式"
                    };
                }

                var rootNode = ParseTokens(tokens);
                var usedFields = ExtractFieldNames(rootNode);

                var filterExpression = new FilterExpression
                {
                    RawExpression = expression,
                    RootNode = rootNode,
                    UsedFields = usedFields,
                    CaseSensitive = caseSensitive,
                    IsValid = true
                };

                return new ExpressionParseResult
                {
                    IsSuccess = true,
                    Expression = filterExpression
                };
            }
            catch (Exception ex)
            {
                return new ExpressionParseResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"解析失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 执行表达式筛选
        /// </summary>
        /// <param name="expression">筛选表达式</param>
        /// <param name="dataRows">数据行集合</param>
        /// <returns>筛选后的数据行</returns>
        public List<Dictionary<string, object?>> FilterData(FilterExpression expression,
            IEnumerable<Dictionary<string, object?>> dataRows)
        {
            if (expression?.RootNode == null)
                return new List<Dictionary<string, object?>>();

            var result = new List<Dictionary<string, object?>>();

            foreach (var row in dataRows)
            {
                try
                {
                    var evalResult = expression.RootNode.Evaluate(row);
                    if (ConvertToBool(evalResult))
                    {
                        result.Add(row);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"行数据筛选失败: {ex.Message}");
                    // 继续处理其他行
                }
            }

            return result;
        }

        /// <summary>
        /// 验证表达式语法
        /// </summary>
        /// <param name="expression">表达式字符串</param>
        /// <returns>验证结果</returns>
        public Models.ExpressionValidationResult ValidateExpression(string expression)
        {
            try
            {
                var parseResult = ParseExpression(expression);
                return new Models.ExpressionValidationResult
                {
                    IsValid = parseResult.IsSuccess,
                    ErrorMessage = parseResult.ErrorMessage ?? string.Empty
                };
            }
            catch (Exception ex)
            {
                return new Models.ExpressionValidationResult
                {
                    IsValid = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        #region 私有方法

        /// <summary>
        /// 将表达式分解为标记
        /// </summary>
        private List<Token> TokenizeExpression(string expression)
        {
            var tokens = new List<Token>();
            var i = 0;

            while (i < expression.Length)
            {
                var c = expression[i];

                // 跳过空白字符
                if (char.IsWhiteSpace(c))
                {
                    i++;
                    continue;
                }

                // 字符串字面量
                if (c == '"' || c == '\'')
                {
                    var quote = c;
                    var start = i + 1;
                    i++;
                    while (i < expression.Length && expression[i] != quote)
                    {
                        i++;
                    }
                    if (i >= expression.Length)
                        throw new ArgumentException("未闭合的字符串");

                    var value = expression.Substring(start, i - start);
                    tokens.Add(new Token(TokenType.String, value));
                    i++;
                    continue;
                }

                // 数字
                if (char.IsDigit(c) || c == '.')
                {
                    var start = i;
                    while (i < expression.Length && (char.IsDigit(expression[i]) || expression[i] == '.'))
                    {
                        i++;
                    }
                    var value = expression.Substring(start, i - start);
                    tokens.Add(new Token(TokenType.Number, value));
                    continue;
                }

                // 运算符
                if (i + 1 < expression.Length)
                {
                    var twoChar = expression.Substring(i, 2);
                    if (BinaryOperators.ContainsKey(twoChar))
                    {
                        tokens.Add(new Token(TokenType.Operator, twoChar));
                        i += 2;
                        continue;
                    }
                }

                if (BinaryOperators.ContainsKey(c.ToString()) || UnaryOperators.ContainsKey(c.ToString()))
                {
                    tokens.Add(new Token(TokenType.Operator, c.ToString()));
                    i++;
                    continue;
                }

                // 括号
                if (c == '(' || c == ')')
                {
                    tokens.Add(new Token(c == '(' ? TokenType.LeftParen : TokenType.RightParen, c.ToString()));
                    i++;
                    continue;
                }

                // 标识符（字段名）
                if (char.IsLetter(c) || c == '_')
                {
                    var start = i;
                    while (i < expression.Length && (char.IsLetterOrDigit(expression[i]) || expression[i] == '_'))
                    {
                        i++;
                    }
                    var value = expression.Substring(start, i - start);

                    // 检查是否为关键字
                    if (UnaryOperators.ContainsKey(value))
                    {
                        tokens.Add(new Token(TokenType.Operator, value));
                    }
                    else
                    {
                        tokens.Add(new Token(TokenType.Identifier, value));
                    }
                    continue;
                }

                throw new ArgumentException($"无效字符: {c}");
            }

            return tokens;
        }

        /// <summary>
        /// 解析标记为表达式树
        /// </summary>
        private ExpressionNode ParseTokens(List<Token> tokens)
        {
            var index = 0;
            return ParseOrExpression(tokens, ref index);
        }

        /// <summary>
        /// 解析 OR 表达式
        /// </summary>
        private ExpressionNode ParseOrExpression(List<Token> tokens, ref int index)
        {
            var left = ParseAndExpression(tokens, ref index);

            while (index < tokens.Count && tokens[index].Value == "||")
            {
                index++; // 跳过 ||
                var right = ParseAndExpression(tokens, ref index);
                left = new BinaryOperatorNode(left, BinaryOperatorType.Or, right);
            }

            return left;
        }

        /// <summary>
        /// 解析 AND 表达式
        /// </summary>
        private ExpressionNode ParseAndExpression(List<Token> tokens, ref int index)
        {
            var left = ParseComparisonExpression(tokens, ref index);

            while (index < tokens.Count && tokens[index].Value == "&&")
            {
                index++; // 跳过 &&
                var right = ParseComparisonExpression(tokens, ref index);
                left = new BinaryOperatorNode(left, BinaryOperatorType.And, right);
            }

            return left;
        }

        /// <summary>
        /// 解析比较表达式
        /// </summary>
        private ExpressionNode ParseComparisonExpression(List<Token> tokens, ref int index)
        {
            var left = ParsePrimaryExpression(tokens, ref index);

            if (index < tokens.Count && BinaryOperators.ContainsKey(tokens[index].Value) &&
                tokens[index].Value != "&&" && tokens[index].Value != "||")
            {
                var op = BinaryOperators[tokens[index].Value];
                index++;
                var right = ParsePrimaryExpression(tokens, ref index);
                return new BinaryOperatorNode(left, op, right);
            }

            return left;
        }

        /// <summary>
        /// 解析基础表达式
        /// </summary>
        private ExpressionNode ParsePrimaryExpression(List<Token> tokens, ref int index)
        {
            if (index >= tokens.Count)
                throw new ArgumentException("意外的表达式结束");

            var token = tokens[index];

            // 一元运算符
            if (token.Type == TokenType.Operator && UnaryOperators.ContainsKey(token.Value))
            {
                index++;
                var operand = ParsePrimaryExpression(tokens, ref index);
                return new UnaryOperatorNode(UnaryOperators[token.Value], operand);
            }

            // 括号表达式
            if (token.Type == TokenType.LeftParen)
            {
                index++; // 跳过 (
                var expr = ParseOrExpression(tokens, ref index);
                if (index >= tokens.Count || tokens[index].Type != TokenType.RightParen)
                    throw new ArgumentException("缺少右括号");
                index++; // 跳过 )
                return expr;
            }

            // 字符串字面量
            if (token.Type == TokenType.String)
            {
                index++;
                return new ConstantNode(token.Value);
            }

            // 数字字面量
            if (token.Type == TokenType.Number)
            {
                index++;
                if (double.TryParse(token.Value, out var number))
                {
                    return new ConstantNode(number);
                }
                throw new ArgumentException($"无效的数字: {token.Value}");
            }

            // 标识符（字段名）
            if (token.Type == TokenType.Identifier)
            {
                index++;
                return new FieldNode(token.Value);
            }

            throw new ArgumentException($"意外的标记: {token.Value}");
        }

        /// <summary>
        /// 提取表达式中的字段名
        /// </summary>
        private List<string> ExtractFieldNames(ExpressionNode? node)
        {
            var fields = new HashSet<string>();
            ExtractFieldNamesRecursive(node, fields);
            return fields.ToList();
        }

        /// <summary>
        /// 递归提取字段名
        /// </summary>
        private void ExtractFieldNamesRecursive(ExpressionNode? node, HashSet<string> fields)
        {
            if (node == null) return;

            switch (node)
            {
                case FieldNode fieldNode:
                    fields.Add(fieldNode.FieldName);
                    break;
                case BinaryOperatorNode binaryNode:
                    ExtractFieldNamesRecursive(binaryNode.Left, fields);
                    ExtractFieldNamesRecursive(binaryNode.Right, fields);
                    break;
                case UnaryOperatorNode unaryNode:
                    ExtractFieldNamesRecursive(unaryNode.Operand, fields);
                    break;
            }
        }

        /// <summary>
        /// 转换为布尔值
        /// </summary>
        private static bool ConvertToBool(object? value)
        {
            return value switch
            {
                bool b => b,
                null => false,
                string s => !string.IsNullOrEmpty(s),
                int i => i != 0,
                double d => d != 0.0,
                _ => true
            };
        }

        #endregion
    }

    /// <summary>
    /// 标记类型
    /// </summary>
    public enum TokenType
    {
        String,
        Number,
        Identifier,
        Operator,
        LeftParen,
        RightParen
    }

    /// <summary>
    /// 标记
    /// </summary>
    public class Token
    {
        public TokenType Type { get; set; }
        public string Value { get; set; }

        public Token(TokenType type, string value)
        {
            Type = type;
            Value = value;
        }

        public override string ToString()
        {
            return $"{Type}: {Value}";
        }
    }
}

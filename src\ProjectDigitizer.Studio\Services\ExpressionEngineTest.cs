using System;
using System.Collections.Generic;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 表达式引擎测试类
    /// 用于验证表达式解析和执行功能
    /// </summary>
    public static class ExpressionEngineTest
    {
        /// <summary>
        /// 运行基础测试
        /// </summary>
        public static void RunBasicTests()
        {
            var engine = new ExpressionEngine();
            
            Console.WriteLine("=== 表达式引擎测试 ===");
            
            // 测试用例
            var testCases = new[]
            {
                // 基础比较
                ("score > 90", true),
                ("name == \"张三\"", true),
                ("age >= 18", true),
                
                // 逻辑组合
                ("score > 90 && class == \"A\"", true),
                ("(age >= 18 && age <= 65) || status == \"VIP\"", true),
                
                // 复杂表达式
                ("!(score < 60) && (class == \"A\" || class == \"B\")", true),
                
                // 错误表达式
                ("score >", false),
                ("(score > 90", false),
                ("score > 90 &&", false)
            };

            foreach (var (expression, shouldSucceed) in testCases)
            {
                Console.WriteLine($"\n测试表达式: {expression}");
                
                var result = engine.ParseExpression(expression);
                
                if (result.IsSuccess == shouldSucceed)
                {
                    Console.WriteLine($"✓ 解析结果符合预期: {(result.IsSuccess ? "成功" : "失败")}");
                    
                    if (result.IsSuccess && result.Expression != null)
                    {
                        Console.WriteLine($"  使用字段: {string.Join(", ", result.Expression.UsedFields)}");
                        Console.WriteLine($"  表达式树: {result.Expression.RootNode}");
                        
                        // 测试执行
                        TestExpressionExecution(engine, result.Expression);
                    }
                    else if (!result.IsSuccess)
                    {
                        Console.WriteLine($"  错误信息: {result.ErrorMessage}");
                    }
                }
                else
                {
                    Console.WriteLine($"✗ 解析结果不符合预期");
                    Console.WriteLine($"  期望: {(shouldSucceed ? "成功" : "失败")}");
                    Console.WriteLine($"  实际: {(result.IsSuccess ? "成功" : "失败")}");
                    if (!string.IsNullOrEmpty(result.ErrorMessage))
                    {
                        Console.WriteLine($"  错误: {result.ErrorMessage}");
                    }
                }
            }
        }

        /// <summary>
        /// 测试表达式执行
        /// </summary>
        private static void TestExpressionExecution(ExpressionEngine engine, FilterExpression expression)
        {
            // 创建测试数据
            var testData = new List<Dictionary<string, object?>>
            {
                new() { ["score"] = 95, ["name"] = "张三", ["class"] = "A", ["age"] = 20, ["status"] = "VIP" },
                new() { ["score"] = 85, ["name"] = "李四", ["class"] = "B", ["age"] = 19, ["status"] = "Normal" },
                new() { ["score"] = 75, ["name"] = "王五", ["class"] = "A", ["age"] = 17, ["status"] = "Normal" },
                new() { ["score"] = 65, ["name"] = "赵六", ["class"] = "C", ["age"] = 21, ["status"] = "VIP" }
            };

            try
            {
                var filteredData = engine.FilterData(expression, testData);
                Console.WriteLine($"  筛选结果: {filteredData.Count}/{testData.Count} 条记录");
                
                foreach (var row in filteredData)
                {
                    var name = row.TryGetValue("name", out var nameValue) ? nameValue?.ToString() : "未知";
                    var score = row.TryGetValue("score", out var scoreValue) ? scoreValue?.ToString() : "未知";
                    Console.WriteLine($"    - {name} (分数: {score})");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行性能测试
        /// </summary>
        public static void RunPerformanceTest()
        {
            var engine = new ExpressionEngine();
            var expression = "score > 80 && (class == \"A\" || class == \"B\") && age >= 18";
            
            Console.WriteLine("\n=== 性能测试 ===");
            Console.WriteLine($"测试表达式: {expression}");
            
            // 解析表达式
            var parseStart = DateTime.Now;
            var parseResult = engine.ParseExpression(expression);
            var parseTime = DateTime.Now - parseStart;
            
            Console.WriteLine($"解析时间: {parseTime.TotalMilliseconds:F2} ms");
            
            if (!parseResult.IsSuccess)
            {
                Console.WriteLine($"解析失败: {parseResult.ErrorMessage}");
                return;
            }

            // 生成大量测试数据
            var testData = GenerateTestData(10000);
            Console.WriteLine($"生成测试数据: {testData.Count} 条记录");

            // 执行筛选
            var filterStart = DateTime.Now;
            var filteredData = engine.FilterData(parseResult.Expression!, testData);
            var filterTime = DateTime.Now - filterStart;
            
            Console.WriteLine($"筛选时间: {filterTime.TotalMilliseconds:F2} ms");
            Console.WriteLine($"筛选结果: {filteredData.Count}/{testData.Count} 条记录");
            Console.WriteLine($"平均每条记录: {filterTime.TotalMilliseconds / testData.Count:F4} ms");
        }

        /// <summary>
        /// 生成测试数据
        /// </summary>
        private static List<Dictionary<string, object?>> GenerateTestData(int count)
        {
            var random = new Random();
            var classes = new[] { "A", "B", "C", "D" };
            var statuses = new[] { "VIP", "Normal", "Bronze" };
            var names = new[] { "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十" };
            
            var data = new List<Dictionary<string, object?>>();
            
            for (int i = 0; i < count; i++)
            {
                data.Add(new Dictionary<string, object?>
                {
                    ["id"] = i + 1,
                    ["score"] = random.Next(0, 101),
                    ["name"] = names[random.Next(names.Length)],
                    ["class"] = classes[random.Next(classes.Length)],
                    ["age"] = random.Next(16, 26),
                    ["status"] = statuses[random.Next(statuses.Length)]
                });
            }
            
            return data;
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                RunBasicTests();
                RunPerformanceTest();
                Console.WriteLine("\n=== 测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
    }
}

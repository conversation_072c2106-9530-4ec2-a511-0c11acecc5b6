using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Application.Services;
using ProjectDigitizer.Infrastructure.Services;

namespace ProjectDigitizer.Application.DependencyInjection;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加应用层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // 注册应用服务
        services.AddScoped<IModuleService, ModuleService>();

        // 注册基础设施服务
        services.AddScoped<IProjectFileService, ProjectFileService>();

        // 注册MediatR
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(ServiceCollectionExtensions).Assembly));

        return services;
    }

    /// <summary>
    /// 添加日志服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddLoggingServices(this IServiceCollection services)
    {
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
            // 可以在这里添加Serilog配置
        });

        return services;
    }
}

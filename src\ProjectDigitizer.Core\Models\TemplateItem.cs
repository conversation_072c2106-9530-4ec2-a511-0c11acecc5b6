using ProjectDigitizer.Core.Enums;

namespace ProjectDigitizer.Core.Models;

/// <summary>
/// 模板项模型
/// </summary>
public class TemplateItem : EntityBase
{
    private string _name = string.Empty;
    private string _description = string.Empty;
    private ModuleType _moduleType;
    private string _iconPath = string.Empty;
    private bool _isEnabled = true;

    /// <summary>
    /// 模板名称
    /// </summary>
    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    /// <summary>
    /// 模板描述
    /// </summary>
    public string Description
    {
        get => _description;
        set => SetProperty(ref _description, value);
    }

    /// <summary>
    /// 对应的模块类型
    /// </summary>
    public ModuleType ModuleType
    {
        get => _moduleType;
        set => SetProperty(ref _moduleType, value);
    }

    /// <summary>
    /// 图标路径
    /// </summary>
    public string IconPath
    {
        get => _iconPath;
        set => SetProperty(ref _iconPath, value);
    }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled
    {
        get => _isEnabled;
        set => SetProperty(ref _isEnabled, value);
    }
}

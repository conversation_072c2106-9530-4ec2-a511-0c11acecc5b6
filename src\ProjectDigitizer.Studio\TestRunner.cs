using System;
using ProjectDigitizer.Studio.Services;
using ProjectDigitizer.Studio.Tests;

namespace ProjectDigitizer.Studio
{
    /// <summary>
    /// 测试运行器 - 用于快速验证新功能
    /// </summary>
    public static class TestRunner
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== ProjectDigitizer DataFilter 功能测试 ===");
            Console.WriteLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();

            // 首先运行属性架构调试
            Console.WriteLine("🔧 运行属性架构调试...");
            Debugging.PropertySchemaDebugger.DebugDataFilterSchema();
            Console.WriteLine();

            // 测试现代化属性面板
            Console.WriteLine("🎨 测试现代化属性面板...");
            TestModernPropertyPanel.RunAllTests();
            Console.WriteLine();

            try
            {
                // 1. 表达式引擎测试
                Console.WriteLine("🔧 运行表达式引擎测试...");
                ExpressionEngineTest.RunAllTests();
                Console.WriteLine();

                // 2. 统计引擎测试
                Console.WriteLine("📊 运行统计引擎测试...");
                AggregationEngineTest.RunAllTests();
                Console.WriteLine();

                // 3. 集成测试
                Console.WriteLine("🚀 运行集成测试...");
                DataFilterIntegrationTest.RunIntegrationTests();
                Console.WriteLine();

                Console.WriteLine("🎉 所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 快速功能演示
        /// </summary>
        public static void QuickDemo()
        {
            Console.WriteLine("=== DataFilter 功能快速演示 ===");

            // 演示表达式解析
            var expressionEngine = new ExpressionEngine();
            var expression = "score > 85 && (class == \"A\" || status == \"VIP\")";

            Console.WriteLine($"测试表达式: {expression}");
            var parseResult = expressionEngine.ParseExpression(expression);

            if (parseResult.IsSuccess)
            {
                Console.WriteLine("✅ 表达式解析成功");
                Console.WriteLine($"使用字段: {string.Join(", ", parseResult.Expression!.UsedFields)}");
            }
            else
            {
                Console.WriteLine($"❌ 表达式解析失败: {parseResult.ErrorMessage}");
            }

            // 演示统计功能
            var aggregationEngine = new AggregationEngine();
            var testData = new[]
            {
                new Dictionary<string, object?> { ["score"] = 95, ["name"] = "张三" },
                new Dictionary<string, object?> { ["score"] = 87, ["name"] = "李四" },
                new Dictionary<string, object?> { ["score"] = 92, ["name"] = "王五" }
            };

            var config = AggregationEngine.CreateConfig(Models.AggregationFunction.Average, "score");
            var result = aggregationEngine.ExecuteAggregation(testData, config);

            Console.WriteLine($"\n统计结果: {result.Description}");
            Console.WriteLine($"执行时间: {result.ExecutionTime.TotalMilliseconds:F2}ms");

            Console.WriteLine("\n演示完成！");
        }
    }
}

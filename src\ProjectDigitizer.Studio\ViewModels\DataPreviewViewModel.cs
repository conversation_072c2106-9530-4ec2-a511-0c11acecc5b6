using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 数据预览视图模型
    /// 用于管理筛选后数据的分页显示
    /// </summary>
    public class DataPreviewViewModel : INotifyPropertyChanged
    {
        private List<Dictionary<string, object?>> _allData = new();
        private List<Dictionary<string, object?>> _filteredData = new();
        private ObservableCollection<DataRowViewModel> _displayedRows = new();
        private List<string> _columnNames = new();
        private int _currentPage = 1;
        private int _pageSize = 50;
        private int _totalPages = 1;
        private string _searchText = string.Empty;
        private bool _isLoading = false;
        private string _statusText = "无数据";

        public DataPreviewViewModel()
        {
            // 初始化命令
            FirstPageCommand = new RelayCommand(GoToFirstPage, CanGoToFirstPage);
            PreviousPageCommand = new RelayCommand(GoToPreviousPage, CanGoToPreviousPage);
            NextPageCommand = new RelayCommand(GoToNextPage, CanGoToNextPage);
            LastPageCommand = new RelayCommand(GoToLastPage, CanGoToLastPage);
            RefreshCommand = new RelayCommand(RefreshData);
            ExportCommand = new RelayCommand(ExportData, CanExportData);
        }

        #region 属性

        /// <summary>
        /// 显示的数据行
        /// </summary>
        public ObservableCollection<DataRowViewModel> DisplayedRows
        {
            get => _displayedRows;
            set => SetProperty(ref _displayedRows, value);
        }

        /// <summary>
        /// 列名列表
        /// </summary>
        public List<string> ColumnNames
        {
            get => _columnNames;
            set => SetProperty(ref _columnNames, value);
        }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                if (SetProperty(ref _currentPage, value))
                {
                    LoadCurrentPage();
                    UpdateCommands();
                }
            }
        }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize
        {
            get => _pageSize;
            set
            {
                if (SetProperty(ref _pageSize, value))
                {
                    CalculatePages();
                    CurrentPage = 1;
                }
            }
        }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages
        {
            get => _totalPages;
            private set => SetProperty(ref _totalPages, value);
        }

        /// <summary>
        /// 搜索文本
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    ApplySearch();
                }
            }
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 状态文本
        /// </summary>
        public string StatusText
        {
            get => _statusText;
            set => SetProperty(ref _statusText, value);
        }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalRecords => _filteredData.Count;

        /// <summary>
        /// 原始记录数
        /// </summary>
        public int OriginalRecords => _allData.Count;

        #endregion

        #region 命令

        public ICommand FirstPageCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand LastPageCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand ExportCommand { get; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置数据源
        /// </summary>
        public void SetData(IEnumerable<Dictionary<string, object?>> data)
        {
            IsLoading = true;
            try
            {
                _allData = data.ToList();
                _filteredData = new List<Dictionary<string, object?>>(_allData);

                // 提取列名
                if (_allData.Count > 0)
                {
                    ColumnNames = _allData[0].Keys.ToList();
                }
                else
                {
                    ColumnNames = new List<string>();
                }

                CalculatePages();
                CurrentPage = 1;
                UpdateStatus();
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 清空数据
        /// </summary>
        public void ClearData()
        {
            _allData.Clear();
            _filteredData.Clear();
            DisplayedRows.Clear();
            ColumnNames.Clear();
            CurrentPage = 1;
            TotalPages = 1;
            StatusText = "无数据";
            UpdateCommands();
        }

        /// <summary>
        /// 刷新数据
        /// </summary>
        public void RefreshData()
        {
            ApplySearch();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 应用搜索筛选
        /// </summary>
        private void ApplySearch()
        {
            IsLoading = true;
            try
            {
                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    _filteredData = new List<Dictionary<string, object?>>(_allData);
                }
                else
                {
                    var searchLower = SearchText.ToLower();
                    _filteredData = _allData.Where(row =>
                        row.Values.Any(value =>
                            value?.ToString()?.ToLower().Contains(searchLower) == true
                        )
                    ).ToList();
                }

                CalculatePages();
                CurrentPage = 1;
                UpdateStatus();
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 计算页数
        /// </summary>
        private void CalculatePages()
        {
            TotalPages = _filteredData.Count == 0 ? 1 : (int)Math.Ceiling((double)_filteredData.Count / PageSize);
            LoadCurrentPage();
        }

        /// <summary>
        /// 加载当前页数据
        /// </summary>
        private void LoadCurrentPage()
        {
            var startIndex = (CurrentPage - 1) * PageSize;
            var pageData = _filteredData.Skip(startIndex).Take(PageSize);

            DisplayedRows.Clear();
            foreach (var row in pageData)
            {
                DisplayedRows.Add(new DataRowViewModel(row, ColumnNames));
            }

            UpdateCommands();
        }

        /// <summary>
        /// 更新状态文本
        /// </summary>
        private void UpdateStatus()
        {
            if (_allData.Count == 0)
            {
                StatusText = "无数据";
            }
            else if (_filteredData.Count == _allData.Count)
            {
                StatusText = $"共 {_allData.Count} 条记录，第 {CurrentPage}/{TotalPages} 页";
            }
            else
            {
                StatusText = $"筛选后 {_filteredData.Count}/{_allData.Count} 条记录，第 {CurrentPage}/{TotalPages} 页";
            }
        }

        /// <summary>
        /// 更新命令状态
        /// </summary>
        private void UpdateCommands()
        {
            OnPropertyChanged(nameof(TotalRecords));
            OnPropertyChanged(nameof(OriginalRecords));
        }

        #endregion

        #region 分页命令实现

        private void GoToFirstPage() => CurrentPage = 1;
        private bool CanGoToFirstPage() => CurrentPage > 1;

        private void GoToPreviousPage() => CurrentPage--;
        private bool CanGoToPreviousPage() => CurrentPage > 1;

        private void GoToNextPage() => CurrentPage++;
        private bool CanGoToNextPage() => CurrentPage < TotalPages;

        private void GoToLastPage() => CurrentPage = TotalPages;
        private bool CanGoToLastPage() => CurrentPage < TotalPages;

        private bool CanExportData() => _filteredData.Count > 0;

        private void ExportData()
        {
            // TODO: 实现数据导出功能
            System.Diagnostics.Debug.WriteLine($"导出 {_filteredData.Count} 条记录");
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// 数据行视图模型
    /// </summary>
    public class DataRowViewModel
    {
        public Dictionary<string, object?> Data { get; }
        public List<string> ColumnNames { get; }

        public DataRowViewModel(Dictionary<string, object?> data, List<string> columnNames)
        {
            Data = data;
            ColumnNames = columnNames;
        }

        /// <summary>
        /// 获取指定列的值
        /// </summary>
        public object? GetValue(string columnName)
        {
            return Data.TryGetValue(columnName, out var value) ? value : null;
        }

        /// <summary>
        /// 获取格式化的值
        /// </summary>
        public string GetFormattedValue(string columnName)
        {
            var value = GetValue(columnName);
            return value?.ToString() ?? "";
        }
    }

    /// <summary>
    /// 简单的命令实现
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add => CommandManager.RequerySuggested += value;
            remove => CommandManager.RequerySuggested -= value;
        }

        public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object? parameter) => _execute();
    }
}

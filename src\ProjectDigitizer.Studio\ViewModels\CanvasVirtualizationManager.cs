using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Threading;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 画布虚拟化管理器 - 优化大量节点的渲染性能
    /// </summary>
    public class CanvasVirtualizationManager
    {
        private readonly DispatcherTimer _viewportUpdateTimer;
        private Rect _currentViewport;
        private double _currentZoom = 1.0;
        private readonly HashSet<ModuleNodeViewModel> _visibleNodes;
        private readonly HashSet<ModuleNodeViewModel> _allNodes;
        private const double VIEWPORT_MARGIN = 100; // 视口边距，提前加载节点

        public CanvasVirtualizationManager()
        {
            _visibleNodes = new HashSet<ModuleNodeViewModel>();
            _allNodes = new HashSet<ModuleNodeViewModel>();

            _viewportUpdateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100) // 10FPS更新频率
            };
            _viewportUpdateTimer.Tick += OnViewportUpdateTimerTick;
        }

        /// <summary>
        /// 当前可见的节点集合
        /// </summary>
        public IEnumerable<ModuleNodeViewModel> VisibleNodes => _visibleNodes;

        /// <summary>
        /// 更新视口信息
        /// </summary>
        public void UpdateViewport(Rect viewport, double zoom)
        {
            _currentViewport = viewport;
            _currentZoom = zoom;

            if (!_viewportUpdateTimer.IsEnabled)
            {
                _viewportUpdateTimer.Start();
            }
        }

        /// <summary>
        /// 添加节点到管理器
        /// </summary>
        public void AddNode(ModuleNodeViewModel node)
        {
            if (node != null)
            {
                _allNodes.Add(node);
                UpdateNodeVisibility();
            }
        }

        /// <summary>
        /// 从管理器移除节点
        /// </summary>
        public void RemoveNode(ModuleNodeViewModel node)
        {
            if (node != null)
            {
                _allNodes.Remove(node);
                _visibleNodes.Remove(node);
            }
        }

        /// <summary>
        /// 清空所有节点
        /// </summary>
        public void Clear()
        {
            _allNodes.Clear();
            _visibleNodes.Clear();
        }

        /// <summary>
        /// 强制更新节点可见性
        /// </summary>
        public void ForceUpdateVisibility()
        {
            UpdateNodeVisibility();
        }

        private void OnViewportUpdateTimerTick(object? sender, EventArgs e)
        {
            UpdateNodeVisibility();
            _viewportUpdateTimer.Stop();
        }

        private void UpdateNodeVisibility()
        {
            if (_allNodes.Count == 0) return;

            // 计算扩展的视口区域
            var expandedViewport = new Rect(
                _currentViewport.X - VIEWPORT_MARGIN,
                _currentViewport.Y - VIEWPORT_MARGIN,
                _currentViewport.Width + 2 * VIEWPORT_MARGIN,
                _currentViewport.Height + 2 * VIEWPORT_MARGIN
            );

            var newVisibleNodes = new HashSet<ModuleNodeViewModel>();

            foreach (var node in _allNodes)
            {
                if (IsNodeInViewport(node, expandedViewport))
                {
                    newVisibleNodes.Add(node);
                }
            }

            // 更新可见节点集合
            var nodesToHide = _visibleNodes.Except(newVisibleNodes).ToList();
            var nodesToShow = newVisibleNodes.Except(_visibleNodes).ToList();

            foreach (var node in nodesToHide)
            {
                _visibleNodes.Remove(node);
                // 可以在这里添加隐藏节点的逻辑
            }

            foreach (var node in nodesToShow)
            {
                _visibleNodes.Add(node);
                // 可以在这里添加显示节点的逻辑
            }

            // 触发可见性变化事件
            if (nodesToHide.Count > 0 || nodesToShow.Count > 0)
            {
                OnVisibilityChanged?.Invoke(newVisibleNodes, nodesToHide, nodesToShow);
            }
        }

        private bool IsNodeInViewport(ModuleNodeViewModel node, Rect viewport)
        {
            // 获取节点的边界矩形
            var nodeRect = new Rect(
                node.Location.X,
                node.Location.Y,
                180, // 节点宽度
                90   // 节点高度
            );

            return viewport.IntersectsWith(nodeRect);
        }

        /// <summary>
        /// 可见性变化事件
        /// </summary>
        public event Action<IEnumerable<ModuleNodeViewModel>, IEnumerable<ModuleNodeViewModel>, IEnumerable<ModuleNodeViewModel>>? OnVisibilityChanged;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _viewportUpdateTimer?.Stop();
            Clear();
        }
    }

    /// <summary>
    /// 画布虚拟化静态管理器
    /// </summary>
    public static class CanvasVirtualizationHelper
    {
        private static readonly Dictionary<object, CanvasVirtualizationManager> _managers = new();

        /// <summary>
        /// 获取或创建虚拟化管理器
        /// </summary>
        public static CanvasVirtualizationManager GetManager(CanvasViewModel canvas)
        {
            if (!_managers.TryGetValue(canvas, out var manager))
            {
                manager = new CanvasVirtualizationManager();
                _managers[canvas] = manager;
            }
            return manager;
        }

        /// <summary>
        /// 移除管理器
        /// </summary>
        public static void RemoveManager(CanvasViewModel canvas)
        {
            if (_managers.TryGetValue(canvas, out var manager))
            {
                manager.Dispose();
                _managers.Remove(canvas);
            }
        }
    }
}

# ProjectDigitizer - 燃气工程数字化处理系统

## 📋 项目简介

ProjectDigitizer 是一个专为燃气工程设计的数字化处理系统，采用类似 Kettle ETL 的可视化节点编排方式，支持从 CAD 图纸数据导入到材料表生成的全流程数字化处理。

## ✨ 核心功能

### 🎨 可视化画布系统
- **节点拖拽**：支持从工具栏拖拽各种功能节点到画布
- **节点连接**：通过连接线建立节点间的数据流关系
- **多选操作**：支持框选、多选节点进行批量操作
- **实时预览**：节点状态实时显示，支持启用/禁用切换

### 🔧 智能属性配置系统
- **动态属性面板**：根据节点类型动态显示对应的属性配置
- **数据来源标识**：通过颜色图标标识属性的数据来源类型
- **分组管理**：属性按功能分组显示，层次清晰
- **实时验证**：属性值实时验证，支持范围检查、格式验证

### 📊 燃气工程专业节点

#### 常规数据类节点
- **平面管线类**：管径、压力等级、材质配置
- **立管类**：楼层数、用户数量管理
- **调压箱调压柜类**：压力调节设备配置
- **开挖回填**：施工开挖参数设置
- **破除恢复**：路面破除恢复标准
- **防腐**：管道防腐工艺配置
- **防雷防静电**：安全防护系统设置

#### 数据衍生关联类节点
- **警示带示踪线**：管道标识系统
- **焊口探伤**：质量检测配置
- **安装台班**：施工人员管理
- **措施**：安全环保措施

#### 处理类节点
- **数据过滤**：条件筛选数据
- **标签搜索**：基于标签的数据检索
- **数据计算**：数学公式计算
- **数据验证**：数据完整性检查

#### 输出类节点
- **文件生成**：多格式文件输出
- **CAD导出**：回写到CAD图纸
- **Excel导出**：材料表生成
- **通知警报**：消息推送系统

## 🎯 数据来源标识系统

系统采用颜色编码标识不同的数据来源：

| 颜色 | 数据来源 | 说明 |
|------|----------|------|
| 🟣 | 项目信息关联 | 来自项目基础信息 |
| 🟠 | CAD信息关联 | 来自CAD图纸数据 |
| 🟢 | 手动输入参数 | 用户手动配置 |
| ⚫ | 固定默认值 | 系统预设值 |
| 🔵 | 材料库（字典） | 来自材料数据库 |
| 🟦 | 规范与图库 | 来自标准规范 |
| 🔴 | 错误信息 | 数据异常标识 |

## 🛠️ 技术架构

### 前端技术栈
- **WPF** - Windows桌面应用框架
- **Nodify** - 节点编辑器控件库
- **MaterialDesign** - UI设计语言
- **MVVM模式** - 数据绑定架构

### 核心组件

#### 属性系统
```
ProjectDigitizer.Studio/Controls/
├── DynamicPropertyPanel.xaml      # 动态属性面板UI
├── PropertySchemaExtensions.cs    # 属性架构扩展
├── IPropertyWidget.cs             # 属性控件接口
├── PropertyWidgetFactory.cs       # 控件工厂
├── TextPropertyWidget.cs          # 文本属性控件
├── NumberPropertyWidget.cs        # 数值属性控件
├── BooleanPropertyWidget.cs       # 布尔属性控件
└── SelectPropertyWidget.cs        # 选择属性控件
```

#### 数据模型
```
ProjectDigitizer.Studio/Models/
├── PropertySchema.cs              # 属性架构定义
├── ModuleModel.cs                 # 节点模型
└── NodePropertyValues.cs          # 属性值容器
```

## 🚀 快速开始

### 环境要求
- .NET 6.0 或更高版本
- Windows 10/11
- Visual Studio 2022 或 Rider

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/ProjectDigitizer.git
cd ProjectDigitizer
```

2. **还原依赖**
```bash
dotnet restore
```

3. **编译运行**
```bash
dotnet build
dotnet run --project ProjectDigitizer.Studio
```

### 基本使用

1. **创建新项目**
   - 启动应用程序
   - 点击"文件" → "新建项目"
   - 输入项目基本信息

2. **添加节点**
   - 从左侧工具栏拖拽节点到画布
   - 双击节点或右键选择"属性"打开配置面板

3. **配置属性**
   - 在属性面板中设置节点参数
   - 注意观察数据来源颜色标识
   - 点击"应用"保存配置

4. **连接节点**
   - 拖拽节点的输出端口到另一个节点的输入端口
   - 建立数据流连接关系

5. **执行流程**
   - 点击工具栏的"运行"按钮
   - 查看执行结果和输出文件

## 📖 使用指南

### 节点类型选择

根据业务需求选择合适的节点类型：

- **数据输入**：使用"平面管线"、"立管"等常规数据类节点
- **数据处理**：使用"数据过滤"、"数据计算"等处理类节点
- **数据输出**：使用"Excel导出"、"CAD导出"等输出类节点

### 属性配置最佳实践

1. **数据来源优先级**：项目信息 > CAD信息 > 材料库 > 手动输入
2. **必填项检查**：注意标有"*"的必填属性
3. **数值范围**：遵循系统提示的数值范围限制
4. **标准规范**：优先使用来自规范库的标准值

### 代码规范
- 遵循C#编码规范
- 添加必要的注释和文档
- 确保单元测试通过


using System;
using System.Globalization;
using System.Windows.Data;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 大于比较转换器
    /// 比较输入值是否大于参数值
    /// </summary>
    public class GreaterThanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                // 尝试将值转换为数字进行比较
                if (value != null && parameter != null)
                {
                    double inputValue = System.Convert.ToDouble(value);
                    double compareValue = System.Convert.ToDouble(parameter);
                    
                    return inputValue > compareValue;
                }
            }
            catch (Exception)
            {
                // 转换失败时返回false
            }
            
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}

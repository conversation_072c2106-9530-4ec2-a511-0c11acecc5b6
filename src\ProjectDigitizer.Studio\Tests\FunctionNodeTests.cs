using System;
using System.Collections.Generic;
using System.Linq;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.Services;
using ProjectDigitizer.Studio.Functions;
using ProjectDigitizer.Studio.Interfaces;

namespace ProjectDigitizer.Studio.Tests
{
    /// <summary>
    /// 函数节点功能测试类
    /// </summary>
    public class FunctionNodeTests
    {
        private readonly FunctionRegistry _functionRegistry;
        private readonly FieldReferenceTracker _referenceTracker;
        private readonly ConnectionStyleSyncService _connectionService;

        public FunctionNodeTests()
        {
            _functionRegistry = FunctionRegistry.Instance;
            _referenceTracker = new FieldReferenceTracker();
            _connectionService = new ConnectionStyleSyncService();
            _connectionService.SetReferenceTracker(_referenceTracker);
        }

        /// <summary>
        /// 测试基础数学函数
        /// </summary>
        public void TestBasicMathFunctions()
        {
            Console.WriteLine("=== 测试基础数学函数 ===");

            // 测试SUM函数
            TestFunction("SUM", new Dictionary<string, object>
            {
                ["values"] = new List<double> { 1, 2, 3, 4, 5 }
            }, 15.0);

            // 测试AVG函数
            TestFunction("AVG", new Dictionary<string, object>
            {
                ["values"] = new List<double> { 1, 2, 3, 4, 5 }
            }, 3.0);

            // 测试MAX函数
            TestFunction("MAX", new Dictionary<string, object>
            {
                ["values"] = new List<double> { 1, 5, 3, 2, 4 }
            }, 5.0);

            // 测试MIN函数
            TestFunction("MIN", new Dictionary<string, object>
            {
                ["values"] = new List<double> { 1, 5, 3, 2, 4 }
            }, 1.0);

            // 测试COUNT函数
            TestFunction("COUNT", new Dictionary<string, object>
            {
                ["values"] = new List<object> { 1, 2, null, 4, 5 }
            }, 4);
        }

        /// <summary>
        /// 测试扩展数学函数
        /// </summary>
        public void TestExtendedMathFunctions()
        {
            Console.WriteLine("\n=== 测试扩展数学函数 ===");

            // 测试ABS函数
            TestFunction("ABS", new Dictionary<string, object>
            {
                ["value"] = -5.0
            }, 5.0);

            // 测试ROUND函数
            TestFunction("ROUND", new Dictionary<string, object>
            {
                ["value"] = 3.14159,
                ["digits"] = 2
            }, 3.14);

            // 测试POWER函数
            TestFunction("POWER", new Dictionary<string, object>
            {
                ["base"] = 2.0,
                ["exponent"] = 3.0
            }, 8.0);

            // 测试SQRT函数
            TestFunction("SQRT", new Dictionary<string, object>
            {
                ["value"] = 16.0
            }, 4.0);
        }

        /// <summary>
        /// 测试字符串函数
        /// </summary>
        public void TestStringFunctions()
        {
            Console.WriteLine("\n=== 测试字符串函数 ===");

            // 测试CONCAT函数
            TestFunction("CONCAT", new Dictionary<string, object>
            {
                ["values"] = new List<object> { "Hello", " ", "World" }
            }, "Hello World");

            // 测试LENGTH函数
            TestFunction("LENGTH", new Dictionary<string, object>
            {
                ["text"] = "Hello"
            }, 5);

            // 测试UPPER函数
            TestFunction("UPPER", new Dictionary<string, object>
            {
                ["text"] = "hello world"
            }, "HELLO WORLD");

            // 测试LOWER函数
            TestFunction("LOWER", new Dictionary<string, object>
            {
                ["text"] = "HELLO WORLD"
            }, "hello world");
        }

        /// <summary>
        /// 测试逻辑函数
        /// </summary>
        public void TestLogicalFunctions()
        {
            Console.WriteLine("\n=== 测试逻辑函数 ===");

            // 测试IF函数
            TestFunction("IF", new Dictionary<string, object>
            {
                ["condition"] = true,
                ["trueValue"] = "是",
                ["falseValue"] = "否"
            }, "是");

            // 测试AND函数
            TestFunction("AND", new Dictionary<string, object>
            {
                ["conditions"] = new List<bool> { true, true, true }
            }, true);

            // 测试OR函数
            TestFunction("OR", new Dictionary<string, object>
            {
                ["conditions"] = new List<bool> { false, true, false }
            }, true);

            // 测试NOT函数
            TestFunction("NOT", new Dictionary<string, object>
            {
                ["condition"] = false
            }, true);
        }

        /// <summary>
        /// 测试字段引用追踪
        /// </summary>
        public void TestFieldReferenceTracking()
        {
            Console.WriteLine("\n=== 测试字段引用追踪 ===");

            // 创建测试字段
            var field1 = new FieldInfo { Name = "field1", DisplayName = "字段1", DataType = FieldDataType.Number };
            var field2 = new FieldInfo { Name = "field2", DisplayName = "字段2", DataType = FieldDataType.Number };
            var field3 = new FieldInfo { Name = "field3", DisplayName = "字段3", DataType = FieldDataType.Number };

            _referenceTracker.RegisterField(field1);
            _referenceTracker.RegisterField(field2);
            _referenceTracker.RegisterField(field3);

            // 测试表达式引用
            var expression1 = "SUM({field1}, {field2})";
            var expression2 = "AVG({field2}, {field3})";

            _referenceTracker.UpdateExpressionReferences("expr1", expression1);
            _referenceTracker.UpdateExpressionReferences("expr2", expression2);

            // 验证引用状态
            Console.WriteLine($"field1 被引用: {_referenceTracker.IsFieldReferenced("field1")}"); // true
            Console.WriteLine($"field2 被引用: {_referenceTracker.IsFieldReferenced("field2")}"); // true
            Console.WriteLine($"field3 被引用: {_referenceTracker.IsFieldReferenced("field3")}"); // true

            // 移除一个表达式
            _referenceTracker.RemoveExpressionReferences("expr1");
            Console.WriteLine($"移除expr1后，field1 被引用: {_referenceTracker.IsFieldReferenced("field1")}"); // false
            Console.WriteLine($"移除expr1后，field2 被引用: {_referenceTracker.IsFieldReferenced("field2")}"); // true (仍被expr2引用)
        }

        /// <summary>
        /// 测试函数注册表
        /// </summary>
        public void TestFunctionRegistry()
        {
            Console.WriteLine("\n=== 测试函数注册表 ===");

            var stats = _functionRegistry.GetStatistics();
            Console.WriteLine($"总函数数量: {stats.TotalFunctions}");

            foreach (var category in stats.CategoryCounts)
            {
                Console.WriteLine($"分类 {category.Key}: {category.Value} 个函数");
            }

            // 测试搜索功能
            var mathFunctions = _functionRegistry.SearchFunctions("数学");
            Console.WriteLine($"搜索'数学'找到 {mathFunctions.Count} 个函数");

            var sumFunctions = _functionRegistry.SearchFunctions("求和");
            Console.WriteLine($"搜索'求和'找到 {sumFunctions.Count} 个函数");
        }

        /// <summary>
        /// 测试单个函数
        /// </summary>
        private void TestFunction(string functionName, Dictionary<string, object> parameters, object expectedResult)
        {
            try
            {
                var result = _functionRegistry.ExecuteFunction(functionName, parameters);

                if (result.IsSuccess)
                {
                    var success = Equals(result.Value, expectedResult);
                    var status = success ? "✓" : "✗";
                    Console.WriteLine($"{status} {functionName}: {result.Value} (期望: {expectedResult}) - {result.ExecutionTimeMs}ms");

                    if (!success)
                    {
                        Console.WriteLine($"  实际类型: {result.Value?.GetType()}, 期望类型: {expectedResult?.GetType()}");
                    }
                }
                else
                {
                    Console.WriteLine($"✗ {functionName}: 执行失败 - {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ {functionName}: 异常 - {ex.Message}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public void RunAllTests()
        {
            Console.WriteLine("开始函数节点功能测试...\n");

            try
            {
                TestBasicMathFunctions();
                TestExtendedMathFunctions();
                TestStringFunctions();
                TestLogicalFunctions();
                TestFieldReferenceTracking();
                TestFunctionRegistry();

                Console.WriteLine("\n=== 测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n测试过程中发生异常: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
        }

        /// <summary>
        /// 性能测试
        /// </summary>
        public void RunPerformanceTests()
        {
            Console.WriteLine("\n=== 性能测试 ===");

            var testData = Enumerable.Range(1, 10000).Select(i => (double)i).ToList();
            var iterations = 1000;

            // 测试SUM函数性能
            var startTime = DateTime.Now;
            for (int i = 0; i < iterations; i++)
            {
                _functionRegistry.ExecuteFunction("SUM", new Dictionary<string, object>
                {
                    ["values"] = testData
                });
            }
            var endTime = DateTime.Now;
            var totalTime = (endTime - startTime).TotalMilliseconds;
            var avgTime = totalTime / iterations;

            Console.WriteLine($"SUM函数性能测试:");
            Console.WriteLine($"  数据量: {testData.Count:N0} 个数值");
            Console.WriteLine($"  执行次数: {iterations:N0} 次");
            Console.WriteLine($"  总时间: {totalTime:F2} ms");
            Console.WriteLine($"  平均时间: {avgTime:F4} ms/次");
        }
    }
}

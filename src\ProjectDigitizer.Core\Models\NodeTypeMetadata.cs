using ProjectDigitizer.Core.Enums;

namespace ProjectDigitizer.Core.Models;

/// <summary>
/// 节点类型元数据
/// </summary>
public class NodeTypeMetadata
{
    public NodeType NodeType { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string IconGlyph { get; set; } = "\uE8F4"; // 默认Segoe MDL2 Assets图标
    public string ColorTheme { get; set; } = "#2196F3"; // 默认蓝色主题
    public List<string> SupportedDataTypes { get; set; } = new();
    public bool AllowMultipleInputs { get; set; } = true;
    public bool AllowMultipleOutputs { get; set; } = true;
}

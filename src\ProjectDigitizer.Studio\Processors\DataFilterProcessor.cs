using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.Services;

namespace ProjectDigitizer.Studio.Processors
{
    /// <summary>
    /// DataFilter 节点处理器
    /// 集成表达式引擎和统计引擎，处理数据筛选和统计
    /// </summary>
    public class DataFilterProcessor
    {
        private readonly ExpressionEngine _expressionEngine;
        private readonly AggregationEngine _aggregationEngine;

        public DataFilterProcessor()
        {
            _expressionEngine = new ExpressionEngine();
            _aggregationEngine = new AggregationEngine();
        }

        /// <summary>
        /// 处理数据筛选和统计
        /// </summary>
        /// <param name="inputData">输入数据</param>
        /// <param name="properties">节点属性</param>
        /// <returns>处理结果</returns>
        public DataFilterResult ProcessData(
            IEnumerable<Dictionary<string, object?>> inputData,
            Dictionary<string, object?> properties)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new DataFilterResult();

            try
            {
                var dataList = inputData.ToList();
                result.OriginalRecordCount = dataList.Count;

                // 解析配置
                var config = ParseConfiguration(properties);
                result.Configuration = config;

                // 验证配置
                var validationResult = ValidateConfiguration(config);
                if (!validationResult.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = string.Join("; ", validationResult.Errors);
                    return result;
                }

                // 执行筛选
                var filteredData = ExecuteFiltering(dataList, config);
                result.FilteredData = filteredData;
                result.FilteredRecordCount = filteredData.Count;

                // 执行统计（如果启用）
                if (config.AggregationEnabled)
                {
                    var aggregationResult = ExecuteAggregation(filteredData, config);
                    result.AggregationResult = aggregationResult;
                }

                result.IsSuccess = true;
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"处理失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"DataFilter处理异常: {ex}");
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        #region 私有方法

        /// <summary>
        /// 解析节点配置
        /// </summary>
        private DataFilterConfiguration ParseConfiguration(Dictionary<string, object?> properties)
        {
            var config = new DataFilterConfiguration();

            // 基础筛选配置
            config.ExpressionMode = GetStringValue(properties, "expressionMode", "simple");
            config.FilterCondition = GetStringValue(properties, "filterCondition", "");
            config.CaseSensitive = GetBoolValue(properties, "caseSensitive", false);
            config.FilterType = GetStringValue(properties, "filterType", "include");

            // 统计配置
            config.AggregationEnabled = GetBoolValue(properties, "aggregationEnabled", false);
            config.AggregationFunction = GetStringValue(properties, "aggregationFunction", "count");
            config.AggregationField = GetStringValue(properties, "aggregationField", "");

            // 显示配置
            config.DisplayMode = GetStringValue(properties, "displayMode", "none");
            config.PreviewEnabled = GetBoolValue(properties, "previewEnabled", false);
            config.MaxPreviewRows = GetIntValue(properties, "maxPreviewRows", 100);

            // 高级选项
            config.ExportEnabled = GetBoolValue(properties, "exportEnabled", true);
            config.CacheResults = GetBoolValue(properties, "cacheResults", true);

            return config;
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        private Models.ValidationResult ValidateConfiguration(DataFilterConfiguration config)
        {
            var result = new Models.ValidationResult();

            // 验证筛选条件
            if (string.IsNullOrWhiteSpace(config.FilterCondition))
            {
                result.AddError("筛选条件不能为空");
                return result;
            }

            // 验证表达式语法
            var expressionResult = _expressionEngine.ValidateExpression(config.FilterCondition);
            if (!expressionResult.IsValid)
            {
                result.AddError($"表达式语法错误: {expressionResult.ErrorMessage}");
                return result;
            }

            // 验证统计配置
            if (config.AggregationEnabled)
            {
                if (config.AggregationFunction != "count" && string.IsNullOrWhiteSpace(config.AggregationField))
                {
                    result.AddError("统计函数需要指定字段名称");
                    return result;
                }
            }

            return result;
        }

        /// <summary>
        /// 执行数据筛选
        /// </summary>
        private List<Dictionary<string, object?>> ExecuteFiltering(
            List<Dictionary<string, object?>> data,
            DataFilterConfiguration config)
        {
            // 解析表达式
            var parseResult = _expressionEngine.ParseExpression(config.FilterCondition, config.CaseSensitive);
            if (!parseResult.IsSuccess || parseResult.Expression == null)
            {
                throw new InvalidOperationException($"表达式解析失败: {parseResult.ErrorMessage}");
            }

            // 执行筛选
            var filteredData = _expressionEngine.FilterData(parseResult.Expression, data);

            // 应用筛选类型
            if (config.FilterType == "exclude")
            {
                // 排除筛选：返回不匹配的记录
                var excludedData = data.Except(filteredData).ToList();
                return excludedData;
            }

            return filteredData;
        }

        /// <summary>
        /// 执行统计计算
        /// </summary>
        private AggregationResultSet ExecuteAggregation(
            List<Dictionary<string, object?>> data,
            DataFilterConfiguration config)
        {
            var aggregationConfigs = new List<AggregationConfig>();

            // 根据配置创建统计配置
            var function = ParseAggregationFunction(config.AggregationFunction);
            var aggregationConfig = AggregationEngine.CreateConfig(function, config.AggregationField);
            aggregationConfigs.Add(aggregationConfig);

            // 执行统计
            var resultSet = _aggregationEngine.ExecuteMultipleAggregations(data, aggregationConfigs);
            resultSet.TotalRecords = data.Count;
            resultSet.FilteredRecords = data.Count;

            return resultSet;
        }

        /// <summary>
        /// 解析统计函数类型
        /// </summary>
        private AggregationFunction ParseAggregationFunction(string functionName)
        {
            return functionName.ToLower() switch
            {
                "count" => AggregationFunction.Count,
                "sum" => AggregationFunction.Sum,
                "avg" or "average" => AggregationFunction.Average,
                "min" => AggregationFunction.Min,
                "max" => AggregationFunction.Max,
                _ => AggregationFunction.Count
            };
        }

        /// <summary>
        /// 获取字符串属性值
        /// </summary>
        private string GetStringValue(Dictionary<string, object?> properties, string key, string defaultValue)
        {
            return properties.TryGetValue(key, out var value) ? value?.ToString() ?? defaultValue : defaultValue;
        }

        /// <summary>
        /// 获取布尔属性值
        /// </summary>
        private bool GetBoolValue(Dictionary<string, object?> properties, string key, bool defaultValue)
        {
            if (properties.TryGetValue(key, out var value))
            {
                if (value is bool boolValue) return boolValue;
                if (bool.TryParse(value?.ToString(), out var parsedValue)) return parsedValue;
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取整数属性值
        /// </summary>
        private int GetIntValue(Dictionary<string, object?> properties, string key, int defaultValue)
        {
            if (properties.TryGetValue(key, out var value))
            {
                if (value is int intValue) return intValue;
                if (int.TryParse(value?.ToString(), out var parsedValue)) return parsedValue;
            }
            return defaultValue;
        }

        #endregion
    }

    /// <summary>
    /// DataFilter 配置
    /// </summary>
    public class DataFilterConfiguration
    {
        public string ExpressionMode { get; set; } = "simple";
        public string FilterCondition { get; set; } = "";
        public bool CaseSensitive { get; set; } = false;
        public string FilterType { get; set; } = "include";

        public bool AggregationEnabled { get; set; } = false;
        public string AggregationFunction { get; set; } = "count";
        public string AggregationField { get; set; } = "";

        public string DisplayMode { get; set; } = "none";
        public bool PreviewEnabled { get; set; } = false;
        public int MaxPreviewRows { get; set; } = 100;

        public bool ExportEnabled { get; set; } = true;
        public bool CacheResults { get; set; } = true;
    }

    /// <summary>
    /// DataFilter 处理结果
    /// </summary>
    public class DataFilterResult
    {
        public bool IsSuccess { get; set; } = false;
        public string? ErrorMessage { get; set; }
        public TimeSpan ExecutionTime { get; set; }

        public DataFilterConfiguration? Configuration { get; set; }

        public int OriginalRecordCount { get; set; }
        public int FilteredRecordCount { get; set; }
        public List<Dictionary<string, object?>> FilteredData { get; set; } = new();

        public AggregationResultSet? AggregationResult { get; set; }

        /// <summary>
        /// 获取处理摘要
        /// </summary>
        public string GetSummary()
        {
            if (!IsSuccess)
                return $"处理失败: {ErrorMessage}";

            var summary = $"筛选: {FilteredRecordCount}/{OriginalRecordCount} 条记录";

            if (AggregationResult != null && AggregationResult.Results.Count > 0)
            {
                var successCount = AggregationResult.Results.Count(r => r.IsSuccess);
                summary += $", 统计: {successCount}/{AggregationResult.Results.Count} 项完成";
            }

            summary += $", 耗时: {ExecutionTime.TotalMilliseconds:F1}ms";

            return summary;
        }
    }
}

﻿<Window x:Class="ProjectDigitizer.Studio.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:ProjectDigitizer.Studio"
        xmlns:controls="clr-namespace:ProjectDigitizer.Studio.Controls"
        xmlns:converters="clr-namespace:ProjectDigitizer.Studio.Converters"
        xmlns:selectors="clr-namespace:ProjectDigitizer.Studio.Selectors"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:nodify="clr-namespace:Nodify;assembly=Nodify"
        xmlns:viewModels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
        mc:Ignorable="d"
        Title="ProjectDigitizer Studio"
        Height="768"
        Width="1024">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/NodeTemplates.xaml"/>
                <ResourceDictionary Source="Styles/ConnectionFlowAnimations.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 常用转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:BooleanToGridLengthConverter x:Key="BooleanToGridLengthConverter"/>
            <converters:BooleanToAngleConverter x:Key="BooleanToAngleConverter"/>
            <converters:MidPointXConverter x:Key="MidPointXConverter"/>
            <converters:MidPointYConverter x:Key="MidPointYConverter"/>
            <converters:GreaterThanConverter x:Key="GreaterThanConverter"/>

            <!-- 节点模板选择器 -->
            <selectors:NodeTypeDataTemplateSelector x:Key="NodeTemplateSelector"
                                                    DefaultTemplate="{StaticResource NodeTemplate}"
                                                    InputNodeTemplate="{StaticResource NodeTemplate}"
                                                    TransformNodeTemplate="{StaticResource NodeTemplate}"
                                                    OutputNodeTemplate="{StaticResource NodeTemplate}"
                                                    ControlNodeTemplate="{StaticResource NodeTemplate}"/>


            <!-- 色彩定义 -->
            <SolidColorBrush x:Key="PrimaryBrush"
                             Color="#4285F4"/>
            <SolidColorBrush x:Key="SecondaryBrush"
                             Color="#34A853"/>
            <SolidColorBrush x:Key="BackgroundBrush"
                             Color="#F5F5F5"/>
            <SolidColorBrush x:Key="SurfaceBrush"
                             Color="#FFFFFF"/>
            <SolidColorBrush x:Key="BorderBrush"
                             Color="#E0E0E0"/>
            <SolidColorBrush x:Key="TextBrush"
                             Color="#333333"/>
            <SolidColorBrush x:Key="DarkBackgroundBrush"
                             Color="#333333"/>

            <!-- 现代化按钮样式 -->
            <Style x:Key="ModernButtonStyle"
                   TargetType="Button">
                <Setter Property="Background"
                        Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground"
                        Value="White"/>
                <Setter Property="BorderThickness"
                        Value="0"/>
                <Setter Property="Padding"
                        Value="12,6"/>
                <Setter Property="Margin"
                        Value="5,2"/>
                <Setter Property="FontSize"
                        Value="12"/>
                <Setter Property="Cursor"
                        Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="3"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver"
                                         Value="True">
                                    <Setter Property="Background"
                                            Value="#3367D6"/>
                                </Trigger>
                                <Trigger Property="IsPressed"
                                         Value="True">
                                    <Setter Property="Background"
                                            Value="#2E5BBA"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- 模板项样式 - 用于Border元素 -->
            <Style x:Key="TemplateItemStyle"
                   TargetType="Border">
                <Setter Property="Background"
                        Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="BorderBrush"
                        Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness"
                        Value="1"/>
                <Setter Property="Margin"
                        Value="5,2"/>
                <Setter Property="Padding"
                        Value="8"/>
                <Setter Property="Cursor"
                        Value="Hand"/>
                <Setter Property="CornerRadius"
                        Value="3"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black"
                                          Opacity="0.1"
                                          ShadowDepth="1"
                                          BlurRadius="3"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver"
                             Value="True">
                        <Setter Property="Background"
                                Value="#F8F9FA"/>
                        <Setter Property="BorderBrush"
                                Value="{StaticResource PrimaryBrush}"/>
                    </Trigger>
                </Style.Triggers>
            </Style>



            <!-- 连接线样式 -->
            <Style x:Key="CustomConnectionStyle"
                   TargetType="nodify:Connection">
                <Setter Property="Stroke"
                        Value="#4A90E2"/>
                <Setter Property="StrokeThickness"
                        Value="2.5"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#4A90E2"
                                          Opacity="0.3"
                                          ShadowDepth="0"
                                          BlurRadius="4"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver"
                             Value="True">
                        <Setter Property="Stroke"
                                Value="#2196F3"/>
                        <Setter Property="StrokeThickness"
                                Value="3"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="#2196F3"
                                                  Opacity="0.5"
                                                  ShadowDepth="0"
                                                  BlurRadius="6"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                    <Trigger Property="IsSelected"
                             Value="True">
                        <Setter Property="Stroke"
                                Value="#1976D2"/>
                        <Setter Property="StrokeThickness"
                                Value="3"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="#1976D2"
                                                  Opacity="0.4"
                                                  ShadowDepth="0"
                                                  BlurRadius="6"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- 挂起连接线样式 -->
            <Style TargetType="nodify:PendingConnection">
                <Setter Property="Stroke"
                        Value="#28A745"/>
                <Setter Property="StrokeThickness"
                        Value="2.5"/>
                <Setter Property="StrokeDashArray"
                        Value="6,3"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#28A745"
                                          Opacity="0.3"
                                          ShadowDepth="0"
                                          BlurRadius="4"/>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- 网格背景定义 -->
            <GeometryDrawing x:Key="SmallGridGeometry"
                             Geometry="M0,0 L0,1 0.03,1 0.03,0.03 1,0.03 1,0 Z"
                             Brush="#303030"/>

            <GeometryDrawing x:Key="LargeGridGeometry"
                             Geometry="M0,0 L0,1 0.015,1 0.015,0.015 1,0.015 1,0 Z"
                             Brush="#404040"/>

            <DrawingBrush x:Key="SmallGridDrawingBrush"
                          TileMode="Tile"
                          ViewportUnits="Absolute"
                          Viewport="0 0 20 20"
                          Transform="{Binding ViewportTransform, ElementName=Canvas}"
                          Drawing="{StaticResource SmallGridGeometry}"/>

            <DrawingBrush x:Key="LargeGridDrawingBrush"
                          TileMode="Tile"
                          ViewportUnits="Absolute"
                          Opacity="0.5"
                          Viewport="0 0 100 100"
                          Transform="{Binding ViewportTransform, ElementName=Canvas}"
                          Drawing="{StaticResource LargeGridGeometry}"/>
        </ResourceDictionary>
    </Window.Resources>
    <DockPanel LastChildFill="True">
        <!-- 菜单栏 -->
        <materialDesign:ColorZone DockPanel.Dock="Top"
                                  Mode="PrimaryDark"
                                  Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 应用标题和图标 -->
                <StackPanel Grid.Column="0"
                            Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Cog"
                                             Width="24"
                                             Height="24"
                                             Foreground="{DynamicResource MaterialDesignDarkForeground}"
                                             VerticalAlignment="Center"
                                             Margin="0,0,12,0"/>
                    <TextBlock Text="ProjectDigitizer Studio"
                               FontSize="18"
                               FontWeight="Medium"
                               Foreground="{DynamicResource MaterialDesignDarkForeground}"
                               VerticalAlignment="Center"/>
                </StackPanel>

                <!-- 主菜单 -->
                <Menu Grid.Column="1"
                      Background="Transparent"
                      Margin="32,0,0,0"
                      VerticalAlignment="Center"
                      HorizontalAlignment="Stretch">
                    <MenuItem Header="文件"
                              Foreground="{DynamicResource MaterialDesignDarkForeground}">
                        <MenuItem Header="新建项目"
                                  Icon="{materialDesign:PackIcon Kind=FileDocumentPlus}"/>
                        <MenuItem Header="保存项目"
                                  Icon="{materialDesign:PackIcon Kind=ContentSave}"/>
                        <MenuItem Header="另存为项目"
                                  Icon="{materialDesign:PackIcon Kind=ContentSaveOutline}"/>
                        <Separator/>
                        <MenuItem Header="退出"
                                  Icon="{materialDesign:PackIcon Kind=ExitToApp}"/>
                    </MenuItem>
                    <MenuItem Header="模板"
                              Foreground="{DynamicResource MaterialDesignDarkForeground}">
                        <MenuItem Header="导入本地模板"
                                  Icon="{materialDesign:PackIcon Kind=Import}"/>
                        <MenuItem Header="导入云端模板"
                                  Icon="{materialDesign:PackIcon Kind=CloudDownload}"/>
                        <MenuItem Header="存为本地模板"
                                  Icon="{materialDesign:PackIcon Kind=ContentSave}"/>
                        <MenuItem Header="存为云端模板"
                                  Icon="{materialDesign:PackIcon Kind=CloudUpload}"/>
                    </MenuItem>
                    <MenuItem Header="数据"
                              Foreground="{DynamicResource MaterialDesignDarkForeground}">
                        <MenuItem Header="CAD数据导入"
                                  Icon="{materialDesign:PackIcon Kind=FileOutline}"/>
                        <MenuItem Header="数据库数据导入"
                                  Icon="{materialDesign:PackIcon Kind=Database}"/>
                        <MenuItem Header="表格数据导入"
                                  Icon="{materialDesign:PackIcon Kind=Table}"/>
                        <MenuItem Header="项目信息"
                                  Icon="{materialDesign:PackIcon Kind=Information}"/>
                    </MenuItem>
                    <MenuItem Header="材料表"
                              Foreground="{DynamicResource MaterialDesignDarkForeground}">
                        <MenuItem Header="导出准备"
                                  Icon="{materialDesign:PackIcon Kind=Export}"/>
                        <MenuItem Header="生成材料表"
                                  Icon="{materialDesign:PackIcon Kind=Table}"/>
                        <MenuItem Header="导出到CAD"
                                  Icon="{materialDesign:PackIcon Kind=FileOutline}"/>
                        <MenuItem Header="导出为excel文件"
                                  Icon="{materialDesign:PackIcon Kind=FileExcel}"/>
                    </MenuItem>
                    <MenuItem Header="工具"
                              Foreground="{DynamicResource MaterialDesignDarkForeground}">
                        <MenuItem Header="导入批注"
                                  Icon="{materialDesign:PackIcon Kind=CommentText}"/>
                        <Separator/>
                        <MenuItem Header="Telerik模板设计器"
                                  Icon="{materialDesign:PackIcon Kind=FileDocument}"
                                  Click="OpenTelerikTemplateDesigner_Click"/>

                    </MenuItem>
                    <MenuItem Header="帮助"
                              Foreground="{DynamicResource MaterialDesignDarkForeground}">
                        <MenuItem Header="用户手册"
                                  Icon="{materialDesign:PackIcon Kind=HelpCircle}"/>
                        <MenuItem Header="关于"
                                  Icon="{materialDesign:PackIcon Kind=Information}"/>
                    </MenuItem>
                </Menu>
            </Grid>
        </materialDesign:ColorZone>

        <!-- 功能区 -->
        <materialDesign:ColorZone DockPanel.Dock="Top"
                                  Mode="PrimaryLight"
                                  Padding="16,8">
            <WrapPanel Orientation="Horizontal">
                <Button ToolTip="新建项目"
                        Margin="0,0,8,0"
                        Background="{DynamicResource MaterialDesignPrimary}"
                        Foreground="{DynamicResource MaterialDesignPrimaryForeground}"
                        BorderThickness="0"
                        Padding="16,8">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileDocumentPlus"
                                                     Width="16"
                                                     Height="16"
                                                     Margin="0,0,4,0"/>
                            <TextBlock Text="新建"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button ToolTip="保存项目"
                        Margin="0,0,8,0"
                        Background="{DynamicResource MaterialDesignPrimary}"
                        Foreground="{DynamicResource MaterialDesignPrimaryForeground}"
                        BorderThickness="0"
                        Padding="16,8">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave"
                                                     Width="16"
                                                     Height="16"
                                                     Margin="0,0,4,0"/>
                            <TextBlock Text="保存"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Separator Margin="8,0"/>

                <Button ToolTip="导入CAD数据"
                        Margin="0,0,8,0"
                        Background="{DynamicResource MaterialDesignPrimary}"
                        Foreground="{DynamicResource MaterialDesignPrimaryForeground}"
                        BorderThickness="0"
                        Padding="16,8">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileOutline"
                                                     Width="16"
                                                     Height="16"
                                                     Margin="0,0,4,0"/>
                            <TextBlock Text="导入CAD"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Separator Margin="8,0"/>

                <Button x:Name="BtnEnableAll"
                        Content="全部启用"
                        ToolTip="启用所有子模板"
                        Click="BtnEnableAll_Click"
                        Margin="0,0,8,0"
                        Background="Transparent"
                        Foreground="{DynamicResource MaterialDesignBody}"
                        BorderBrush="{DynamicResource MaterialDesignPrimary}"
                        BorderThickness="1"
                        Padding="16,8"/>

                <Button x:Name="BtnDisableAll"
                        Content="全部关闭"
                        ToolTip="关闭所有子模板"
                        Click="BtnDisableAll_Click"
                        Margin="0,0,8,0"
                        Background="Transparent"
                        Foreground="{DynamicResource MaterialDesignBody}"
                        BorderBrush="{DynamicResource MaterialDesignPrimary}"
                        BorderThickness="1"
                        Padding="16,8"/>

                <Button x:Name="BtnClearCanvas"
                        Content="清空画布"
                        ToolTip="清空所有子模板"
                        Click="BtnClearCanvas_Click"
                        Margin="0,0,8,0"
                        Background="Transparent"
                        Foreground="{DynamicResource MaterialDesignBody}"
                        BorderBrush="{DynamicResource MaterialDesignPrimary}"
                        BorderThickness="1"
                        Padding="16,8"/>

                <Button x:Name="BtnDeleteSelected"
                        Content="删除选中"
                        ToolTip="删除选中的子模板"
                        Click="BtnDeleteSelected_Click"
                        Margin="0,0,8,0"
                        Background="Transparent"
                        Foreground="{DynamicResource MaterialDesignBody}"
                        BorderBrush="{DynamicResource MaterialDesignPrimary}"
                        BorderThickness="1"
                        Padding="16,8"/>

                <Separator Margin="8,0"/>

                <Button x:Name="BtnHideClosedModules"
                        Content="隐藏关闭的模板"
                        ToolTip="隐藏所有关闭的模板"
                        Click="BtnHideClosedModules_Click"
                        Margin="0,0,8,0"
                        Background="Transparent"
                        Foreground="{DynamicResource MaterialDesignBody}"
                        BorderThickness="0"
                        Padding="16,8"/>
            </WrapPanel>
        </materialDesign:ColorZone>

        <!-- 状态栏 -->
        <materialDesign:ColorZone DockPanel.Dock="Bottom"
                                  Mode="PrimaryLight"
                                  Padding="16,4">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0"
                            Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CheckCircle"
                                             Width="16"
                                             Height="16"
                                             Foreground="Green"
                                             VerticalAlignment="Center"
                                             Margin="0,0,8,0"/>
                    <TextBlock Text="就绪"
                               VerticalAlignment="Center"
                               Foreground="{DynamicResource MaterialDesignBody}"/>
                </StackPanel>

                <StackPanel Grid.Column="1"
                            Orientation="Horizontal">
                    <TextBlock Text="节点数量: "
                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                               VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding Nodes.Count}"
                               Foreground="{DynamicResource MaterialDesignBody}"
                               VerticalAlignment="Center"
                               Margin="0,0,16,0"/>
                    <TextBlock Text="连接数量: "
                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                               VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding Connections.Count}"
                               Foreground="{DynamicResource MaterialDesignBody}"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- 主体内容 -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧子模板选择区域 -->
            <materialDesign:Card Grid.Column="0"
                                 Margin="8"
                                 materialDesign:ElevationAssist.Elevation="Dp4">
                <DockPanel Background="{DynamicResource MaterialDesignPaper}">
                    <!-- 模板选择器头部 -->
                    <materialDesign:ColorZone DockPanel.Dock="Top"
                                              Mode="PrimaryMid"
                                              Padding="16">
                        <StackPanel>
                            <TextBlock Text="工作流模块库"
                                       FontSize="18"
                                       FontWeight="Medium"
                                       Foreground="{DynamicResource MaterialDesignDarkForeground}"/>
                            <TextBlock Text="拖拽模块到画布创建工作流"
                                       FontSize="12"
                                       Opacity="0.7"
                                       Foreground="{DynamicResource MaterialDesignDarkForeground}"
                                       Margin="0,4,0,0"/>
                        </StackPanel>
                    </materialDesign:ColorZone>

                    <!-- 动态模板分类列表 -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                  materialDesign:ScrollViewerAssist.IsAutoHideEnabled="True">
                        <ItemsControl ItemsSource="{Binding TemplateCategories}"
                                      Margin="12">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <materialDesign:Card Margin="0,0,0,16"
                                                         materialDesign:ElevationAssist.Elevation="Dp2">
                                        <StackPanel>
                                            <!-- 分类标题 -->
                                            <materialDesign:ColorZone Mode="PrimaryLight"
                                                                      Padding="12,8">
                                                <StackPanel Orientation="Horizontal">
                                                    <materialDesign:PackIcon Kind="Widgets"
                                                                             Width="20"
                                                                             Height="20"
                                                                             Margin="0,0,8,0"
                                                                             Foreground="{DynamicResource MaterialDesignDarkForeground}"/>
                                                    <TextBlock Text="{Binding Name}"
                                                               FontSize="14"
                                                               FontWeight="Medium"
                                                               Foreground="{DynamicResource MaterialDesignDarkForeground}"/>
                                                </StackPanel>
                                            </materialDesign:ColorZone>

                                            <!-- 分类中的模板项 -->
                                            <ItemsControl ItemsSource="{Binding Items}"
                                                          Margin="8">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <Border Background="Transparent"
                                                                BorderThickness="0"
                                                                Margin="0,2"
                                                                Padding="12,8"
                                                                ToolTip="{Binding Description}"
                                                                Cursor="Hand"
                                                                MouseDown="TemplateItem_MouseDown"
                                                                MouseMove="TemplateItem_MouseMove"
                                                                MouseUp="TemplateItem_MouseUp">
                                                            <Border.Style>
                                                                <Style TargetType="Border">
                                                                    <Setter Property="Background"
                                                                            Value="Transparent"/>
                                                                    <Style.Triggers>
                                                                        <Trigger Property="IsMouseOver"
                                                                                 Value="True">
                                                                            <Setter Property="Background"
                                                                                    Value="#F0F0F0"/>
                                                                        </Trigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </Border.Style>
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto"/>
                                                                    <ColumnDefinition Width="*"/>
                                                                </Grid.ColumnDefinitions>

                                                                <!-- 模块图标 -->
                                                                <Border Grid.Column="0"
                                                                        Width="8"
                                                                        Height="32"
                                                                        CornerRadius="4,0,0,4"
                                                                        Margin="0,0,12,0">
                                                                    <Border.Background>
                                                                        <SolidColorBrush Color="{Binding DataContext.Color, RelativeSource={RelativeSource AncestorType=ItemsControl}}"/>
                                                                    </Border.Background>
                                                                </Border>

                                                                <!-- 模块信息 -->
                                                                <StackPanel Grid.Column="1">
                                                                    <TextBlock Text="{Binding Name}"
                                                                               FontSize="13"
                                                                               FontWeight="Medium"
                                                                               Foreground="{DynamicResource MaterialDesignBody}"/>
                                                                    <TextBlock Text="{Binding Description}"
                                                                               FontSize="11"
                                                                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                               TextWrapping="Wrap"
                                                                               Margin="0,2,0,0"/>
                                                                </StackPanel>
                                                            </Grid>
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </StackPanel>
                                    </materialDesign:Card>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </DockPanel>
            </materialDesign:Card>

            <!-- 右侧主要内容区域 -->
            <TabControl Grid.Column="1"
                        Margin="5">
                <TabItem Header="模板">
                    <!-- 画布区域 -->
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 画布操作工具栏 -->
                        <ToolBar Grid.Row="0">
                            <Button x:Name="BtnEnableAllCanvas"
                                    Content="全部启用"
                                    ToolTip="启用所有子模板"
                                    Click="BtnEnableAll_Click"/>
                            <Button x:Name="BtnDisableAllCanvas"
                                    Content="全部关闭"
                                    ToolTip="关闭所有子模板"
                                    Click="BtnDisableAll_Click"/>
                            <Button x:Name="BtnClearCanvasArea"
                                    Content="清空画布"
                                    ToolTip="清空所有子模板"
                                    Click="BtnClearCanvas_Click"/>
                            <Button x:Name="BtnDeleteSelectedCanvas"
                                    Content="删除选中"
                                    ToolTip="删除选中的子模板"
                                    Click="BtnDeleteSelected_Click"/>
                            <Separator/>
                            <Button x:Name="BtnHideClosedModulesCanvas"
                                    Content="隐藏关闭的模板"
                                    ToolTip="隐藏所有关闭的模板"
                                    Click="BtnHideClosedModules_Click"/>
                            <Separator/>
                            <!-- 自动布局功能按钮 -->
                            <Button x:Name="BtnAutoLayoutHierarchical"
                                    Content="自动布局"
                                    ToolTip="按照数据流方向自动排列节点，支持多行换行"
                                    Click="BtnAutoLayoutHierarchical_Click">
                                <Button.Style>
                                    <Style TargetType="Button"
                                           BasedOn="{StaticResource {x:Type Button}}">
                                        <Setter Property="Background"
                                                Value="#E3F2FD"/>
                                        <Setter Property="BorderBrush"
                                                Value="#2196F3"/>
                                        <Setter Property="Foreground"
                                                Value="#1976D2"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver"
                                                     Value="True">
                                                <Setter Property="Background"
                                                        Value="#BBDEFB"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </ToolBar>

                        <!-- Nodify画布 -->
                        <Grid Grid.Row="1"
                              Background="{StaticResource SurfaceBrush}">
                            <!-- 网格背景 -->
                            <Rectangle Fill="#F8F9FA"
                                       Stroke="#E0E0E0"
                                       StrokeThickness="1"
                                       StrokeDashArray="5,5"
                                       Opacity="0.3"/>

                            <nodify:NodifyEditor x:Name="Canvas"
                                                 ItemsSource="{Binding Nodes}"
                                                 Connections="{Binding Connections}"
                                                 SelectedItems="{Binding SelectedItems}"
                                                 PendingConnection="{Binding PendingConnection}"
                                                 ConnectionCompletedCommand="{Binding CreateConnectionCommand}"
                                                 DisconnectConnectorCommand="{Binding DisconnectConnectorCommand}"
                                                 RemoveConnectionCommand="{Binding RemoveConnectionCommand}"
                                                 ItemTemplateSelector="{StaticResource NodeTemplateSelector}"
                                                 Background="#F5F5F5"
                                                 AllowDrop="True"
                                                 Drop="Canvas_Drop"
                                                 DragOver="Canvas_DragOver"
                                                 PreviewMouseDown="Canvas_PreviewMouseDown"
                                                 SelectionChanged="Canvas_SelectionChanged"
                                                 ViewportUpdated="Canvas_ViewportUpdated">

                                <!-- 连接线模板 - 包含序号标识的Canvas结构 -->
                                <nodify:NodifyEditor.ConnectionTemplate>
                                    <DataTemplate>
                                        <!-- 使用Canvas来精确定位连接线和序号标识 -->
                                        <Canvas>
                                            <!-- 透明的Hit Test区域 - 扩大点击范围到8像素 -->
                                            <nodify:LineConnection x:Name="HitTestConnection"
                                                                   Source="{Binding Source.Anchor}"
                                                                   Target="{Binding Target.Anchor}"
                                                                   Stroke="Transparent"
                                                                   StrokeThickness="8"
                                                                   DirectionalArrowsCount="0"
                                                                   MouseRightButtonDown="Connection_MouseRightButtonDown"/>

                                            <!-- 可见的连接线 - 使用动态颜色 -->
                                            <nodify:LineConnection x:Name="VisualConnection"
                                                                   Source="{Binding Source.Anchor}"
                                                                   Target="{Binding Target.Anchor}"
                                                                   Stroke="{Binding ConnectionColor}"
                                                                   StrokeThickness="2.5"
                                                                   DirectionalArrowsCount="0"
                                                                   Opacity="0.85"
                                                                   IsHitTestVisible="False">
                                                <nodify:LineConnection.Style>
                                                    <Style TargetType="nodify:LineConnection">
                                                        <Style.Triggers>
                                                            <!-- 字段未被引用状态 - 虚线 -->
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}"
                                                                               Value="True"/>
                                                                    <Condition Binding="{Binding IsFieldReferenced}"
                                                                               Value="False"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="StrokeDashArray"
                                                                        Value="5,3"/>
                                                                <Setter Property="Opacity"
                                                                        Value="0.6"/>
                                                            </MultiDataTrigger>

                                                            <!-- 字段被引用状态 - 实线 -->
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}"
                                                                               Value="True"/>
                                                                    <Condition Binding="{Binding IsFieldReferenced}"
                                                                               Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="StrokeDashArray"
                                                                        Value=""/>
                                                                <Setter Property="Opacity"
                                                                        Value="0.85"/>
                                                                <Setter Property="StrokeThickness"
                                                                        Value="3"/>
                                                            </MultiDataTrigger>

                                                            <!-- 禁用状态 -->
                                                            <DataTrigger Binding="{Binding IsEnabled}"
                                                                         Value="False">
                                                                <Setter Property="Stroke"
                                                                        Value="#9E9E9E"/>
                                                                <Setter Property="Opacity"
                                                                        Value="0.4"/>
                                                                <Setter Property="StrokeDashArray"
                                                                        Value="6,4"/>
                                                            </DataTrigger>
                                                            <!-- 鼠标悬停状态 - 基于Hit Test连接线的悬停状态 -->
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}"
                                                                               Value="True"/>
                                                                    <Condition Binding="{Binding ElementName=HitTestConnection, Path=IsMouseOver}"
                                                                               Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Stroke"
                                                                        Value="#1976D2"/>
                                                                <Setter Property="StrokeThickness"
                                                                        Value="3.5"/>
                                                                <Setter Property="Opacity"
                                                                        Value="1.0"/>
                                                            </MultiDataTrigger>
                                                            <!-- 选中状态 - 基于Hit Test连接线的选中状态 -->
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}"
                                                                               Value="True"/>
                                                                    <Condition Binding="{Binding ElementName=HitTestConnection, Path=IsSelected}"
                                                                               Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Stroke"
                                                                        Value="#FF9800"/>
                                                                <Setter Property="StrokeThickness"
                                                                        Value="3.5"/>
                                                                <Setter Property="Opacity"
                                                                        Value="1.0"/>
                                                            </MultiDataTrigger>
                                                            <!-- 禁用状态下的鼠标悬停 - 基于Hit Test连接线的悬停状态 -->
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}"
                                                                               Value="False"/>
                                                                    <Condition Binding="{Binding ElementName=HitTestConnection, Path=IsMouseOver}"
                                                                               Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Stroke"
                                                                        Value="#BDBDBD"/>
                                                                <Setter Property="Opacity"
                                                                        Value="0.6"/>
                                                            </MultiDataTrigger>
                                                            <!-- 禁用状态下的选中 - 基于Hit Test连接线的选中状态 -->
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}"
                                                                               Value="False"/>
                                                                    <Condition Binding="{Binding ElementName=HitTestConnection, Path=IsSelected}"
                                                                               Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Stroke"
                                                                        Value="#FF9800"/>
                                                                <Setter Property="Opacity"
                                                                        Value="0.7"/>
                                                                <Setter Property="StrokeThickness"
                                                                        Value="3.5"/>
                                                            </MultiDataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </nodify:LineConnection.Style>
                                            </nodify:LineConnection>

                                            <!-- 流动光效层 - 在连接线上方显示流动动画 -->
                                            <nodify:LineConnection x:Name="FlowEffectConnection"
                                                                   Source="{Binding Source.Anchor}"
                                                                   Target="{Binding Target.Anchor}"
                                                                   StrokeThickness="3"
                                                                   DirectionalArrowsCount="0"
                                                                   Stroke="{StaticResource BlueFlowGradientBrush}"
                                                                   Opacity="1.0"
                                                                   IsHitTestVisible="False">
                                                <nodify:LineConnection.Style>
                                                    <Style TargetType="nodify:LineConnection">
                                                        <!-- 流动动画资源 -->
                                                        <Style.Resources>
                                                            <Storyboard x:Key="FlowAnimation"
                                                                        RepeatBehavior="Forever">
                                                                <DoubleAnimation Storyboard.TargetProperty="(nodify:LineConnection.Stroke).(LinearGradientBrush.Transform).(TranslateTransform.X)"
                                                                                 From="-200"
                                                                                 To="400"
                                                                                 Duration="0:0:2">
                                                                    <DoubleAnimation.EasingFunction>
                                                                        <QuadraticEase EasingMode="EaseInOut"/>
                                                                    </DoubleAnimation.EasingFunction>
                                                                </DoubleAnimation>
                                                            </Storyboard>
                                                        </Style.Resources>
                                                        <Style.Triggers>
                                                            <!-- 启用流动动画时开始流动 -->
                                                            <EventTrigger RoutedEvent="Loaded">
                                                                <BeginStoryboard Storyboard="{StaticResource FlowAnimation}"/>
                                                            </EventTrigger>
                                                            <!-- 启用流动动画时显示光效 -->
                                                            <DataTrigger Binding="{Binding IsFlowAnimationEnabled}"
                                                                         Value="True">
                                                                <Setter Property="Opacity"
                                                                        Value="1.0"/>
                                                            </DataTrigger>

                                                            <!-- 慢速流动状态 -->
                                                            <DataTrigger Binding="{Binding FlowState}"
                                                                         Value="Slow">
                                                                <Setter Property="Stroke"
                                                                        Value="#802196F3"/>
                                                                <Setter Property="StrokeThickness"
                                                                        Value="3"/>
                                                                <DataTrigger.EnterActions>
                                                                    <BeginStoryboard>
                                                                        <Storyboard RepeatBehavior="Forever">
                                                                            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                                                                             From="0.2"
                                                                                             To="0.8"
                                                                                             Duration="0:0:2"
                                                                                             AutoReverse="True">
                                                                                <DoubleAnimation.EasingFunction>
                                                                                    <SineEase EasingMode="EaseInOut"/>
                                                                                </DoubleAnimation.EasingFunction>
                                                                            </DoubleAnimation>
                                                                        </Storyboard>
                                                                    </BeginStoryboard>
                                                                </DataTrigger.EnterActions>
                                                            </DataTrigger>

                                                            <!-- 正常流动状态 -->
                                                            <DataTrigger Binding="{Binding FlowState}"
                                                                         Value="Normal">
                                                                <Setter Property="Stroke"
                                                                        Value="#FF2196F3"/>
                                                                <Setter Property="StrokeThickness"
                                                                        Value="4"/>
                                                                <DataTrigger.EnterActions>
                                                                    <BeginStoryboard>
                                                                        <Storyboard RepeatBehavior="Forever">
                                                                            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                                                                             From="0.3"
                                                                                             To="1.0"
                                                                                             Duration="0:0:1"
                                                                                             AutoReverse="True">
                                                                                <DoubleAnimation.EasingFunction>
                                                                                    <SineEase EasingMode="EaseInOut"/>
                                                                                </DoubleAnimation.EasingFunction>
                                                                            </DoubleAnimation>
                                                                        </Storyboard>
                                                                    </BeginStoryboard>
                                                                </DataTrigger.EnterActions>
                                                            </DataTrigger>

                                                            <!-- 快速流动状态 -->
                                                            <DataTrigger Binding="{Binding FlowState}"
                                                                         Value="Fast">
                                                                <Setter Property="Stroke"
                                                                        Value="{StaticResource GreenFlowGradientBrush}"/>
                                                                <DataTrigger.EnterActions>
                                                                    <BeginStoryboard>
                                                                        <Storyboard RepeatBehavior="Forever">
                                                                            <DoubleAnimation Storyboard.TargetProperty="(nodify:LineConnection.Stroke).(LinearGradientBrush.Transform).(TranslateTransform.X)"
                                                                                             From="-100"
                                                                                             To="200"
                                                                                             Duration="0:0:1">
                                                                                <DoubleAnimation.EasingFunction>
                                                                                    <SineEase EasingMode="EaseInOut"/>
                                                                                </DoubleAnimation.EasingFunction>
                                                                            </DoubleAnimation>
                                                                        </Storyboard>
                                                                    </BeginStoryboard>
                                                                </DataTrigger.EnterActions>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </nodify:LineConnection.Style>
                                            </nodify:LineConnection>

                                            <!-- 连接序号标识 - 显示在连接线中点 -->
                                            <Border x:Name="ConnectionIndexBadge"
                                                    Background="{Binding ConnectionColor}"
                                                    BorderBrush="White"
                                                    BorderThickness="2"
                                                    CornerRadius="8"
                                                    Width="16"
                                                    Height="16"
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"
                                                    Visibility="{Binding Source.HasMultipleConnections, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#000000"
                                                                      Opacity="0.3"
                                                                      ShadowDepth="1"
                                                                      BlurRadius="3"/>
                                                </Border.Effect>
                                                <TextBlock Text="{Binding ConnectionIndex}"
                                                           Foreground="White"
                                                           FontSize="9"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                            </Border>

                                            <!-- 连接序号标识 - 跟随连接线动态定位 -->
                                            <Border Background="{Binding ConnectionColor}"
                                                    BorderBrush="White"
                                                    BorderThickness="2"
                                                    CornerRadius="8"
                                                    Width="16"
                                                    Height="16">
                                                <Border.RenderTransform>
                                                    <TranslateTransform>
                                                        <TranslateTransform.X>
                                                            <MultiBinding Converter="{StaticResource MidPointXConverter}">
                                                                <Binding Path="Source.Anchor.X"/>
                                                                <Binding Path="Target.Anchor.X"/>
                                                            </MultiBinding>
                                                        </TranslateTransform.X>
                                                        <TranslateTransform.Y>
                                                            <MultiBinding Converter="{StaticResource MidPointYConverter}">
                                                                <Binding Path="Source.Anchor.Y"/>
                                                                <Binding Path="Target.Anchor.Y"/>
                                                            </MultiBinding>
                                                        </TranslateTransform.Y>
                                                    </TranslateTransform>
                                                </Border.RenderTransform>
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Setter Property="Visibility"
                                                                Value="Collapsed"/>
                                                        <Style.Triggers>
                                                            <!-- 基于连接器连接数量显示序号标识 -->
                                                            <DataTrigger Binding="{Binding Source.ConnectionCount, Converter={StaticResource GreaterThanConverter}, ConverterParameter=1}"
                                                                         Value="True">
                                                                <Setter Property="Visibility"
                                                                        Value="Visible"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#000000"
                                                                      Opacity="0.3"
                                                                      ShadowDepth="1"
                                                                      BlurRadius="3"/>
                                                </Border.Effect>
                                                <TextBlock Text="{Binding ConnectionIndex}"
                                                           Foreground="White"
                                                           FontSize="9"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                            </Border>
                                        </Canvas>
                                    </DataTemplate>
                                </nodify:NodifyEditor.ConnectionTemplate>

                                <!-- 挂起连接模板 -->
                                <nodify:NodifyEditor.PendingConnectionTemplate>
                                    <DataTemplate>
                                        <nodify:PendingConnection StartedCommand="{Binding StartCommand}"
                                                                  CompletedCommand="{Binding FinishCommand}"
                                                                  Stroke="#4CAF50"
                                                                  StrokeThickness="2.5"
                                                                  StrokeDashArray="8,4"
                                                                  Opacity="0.8"
                                                                  AllowOnlyConnectors="True">
                                            <nodify:PendingConnection.Effect>
                                                <DropShadowEffect Color="#4CAF50"
                                                                  Opacity="0.4"
                                                                  ShadowDepth="0"
                                                                  BlurRadius="6"/>
                                            </nodify:PendingConnection.Effect>
                                        </nodify:PendingConnection>
                                    </DataTemplate>
                                </nodify:NodifyEditor.PendingConnectionTemplate>

                                <!-- 节点容器样式 - 完全覆盖Nodify默认样式 -->
                                <nodify:NodifyEditor.ItemContainerStyle>
                                    <Style TargetType="nodify:ItemContainer">
                                        <Setter Property="Location"
                                                Value="{Binding Location}"/>

                                        <Setter Property="BorderThickness"
                                                Value="0"/>
                                        <Setter Property="SelectedBorderThickness"
                                                Value="0"/>
                                        <Setter Property="BorderBrush"
                                                Value="Transparent"/>
                                        <Setter Property="SelectedBrush"
                                                Value="Transparent"/>
                                        <Setter Property="Background"
                                                Value="Transparent"/>
                                        <Setter Property="Foreground"
                                                Value="Transparent"/>

                                        <Setter Property="Effect"
                                                Value="{x:Null}"/>
                                        <Setter Property="Opacity"
                                                Value="1"/>

                                        <Setter Property="Margin"
                                                Value="0"/>
                                        <Setter Property="Padding"
                                                Value="0"/>

                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="nodify:ItemContainer">
                                                    <Border Background="Transparent"
                                                            BorderBrush="Transparent"
                                                            BorderThickness="0"
                                                            Margin="0">
                                                        <ContentPresenter/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>

                                        <EventSetter Event="Selected"
                                                     Handler="ItemContainer_Selected"/>
                                        <EventSetter Event="MouseDown"
                                                     Handler="ItemContainer_MouseDown"/>
                                        <EventSetter Event="MouseDoubleClick"
                                                     Handler="ItemContainer_MouseDoubleClick"/>

                                        <Style.Triggers>
                                            <!-- 锁定状态下的视觉提示 - 改为手型光标，表示可以交互但位置锁定 -->
                                            <DataTrigger Binding="{Binding IsLocked}"
                                                         Value="True">
                                                <Setter Property="Cursor"
                                                        Value="Hand"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </nodify:NodifyEditor.ItemContainerStyle>
                            </nodify:NodifyEditor>

                            <!-- 画布缩放控制器 -->
                            <controls:CanvasZoomControl x:Name="ZoomControl"
                                                        HorizontalAlignment="Right"
                                                        VerticalAlignment="Bottom"
                                                        Margin="16"
                                                        ZoomChanged="ZoomControl_ZoomChanged"
                                                        ResetZoomRequested="ZoomControl_ResetZoom"
                                                        FitToCanvasRequested="ZoomControl_FitToCanvas"/>
                        </Grid>
                    </Grid>
                </TabItem>
                <TabItem Header="说明">
                    <TextBlock Text="说明内容"
                               Margin="10"/>
                </TabItem>
                <TabItem Header="项目信息">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0"
                                   Grid.Column="0"
                                   Text="项目名称:"
                                   Margin="0,0,10,5"/>
                        <TextBox Grid.Row="0"
                                 Grid.Column="1"
                                 Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="1"
                                   Grid.Column="0"
                                   Text="项目描述:"
                                   Margin="0,0,10,0"
                                   VerticalAlignment="Top"/>
                        <TextBox Grid.Row="1"
                                 Grid.Column="1"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 VerticalAlignment="Stretch"/>
                    </Grid>
                </TabItem>
                <TabItem Header="材料偏好">
                    <TextBlock Text="材料偏好设置"
                               Margin="10"/>
                </TabItem>
                <TabItem Header="材料表">
                    <TextBlock Text="材料表内容"
                               Margin="10"/>
                </TabItem>
            </TabControl>

            <!-- 右侧可折叠属性面板 -->
            <Border Grid.Column="2"
                    x:Name="PropertyPanelBorder"
                    Background="{DynamicResource MaterialDesignPaper}"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="1,0,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 折叠/展开按钮 -->
                    <Border Grid.Column="0"
                            Width="24"
                            Background="{DynamicResource MaterialDesignCardBackground}"
                            BorderBrush="{DynamicResource MaterialDesignDivider}"
                            BorderThickness="0,0,1,0">
                        <ToggleButton x:Name="PropertyPanelToggle"
                                      Width="20"
                                      Height="60"
                                      VerticalAlignment="Center"
                                      Background="Transparent"
                                      BorderThickness="0"
                                      IsChecked="True"
                                      ToolTip="折叠/展开属性面板">
                            <ToggleButton.Content>
                                <TextBlock Text="◀"
                                           FontSize="12"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                            </ToggleButton.Content>
                            <ToggleButton.Style>
                                <Style TargetType="ToggleButton">
                                    <Style.Triggers>
                                        <Trigger Property="IsChecked"
                                                 Value="False">
                                            <Setter Property="Content">
                                                <Setter.Value>
                                                    <TextBlock Text="▶"
                                                               FontSize="12"/>
                                                </Setter.Value>
                                            </Setter>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </ToggleButton.Style>
                        </ToggleButton>
                    </Border>

                    <!-- 属性面板内容 -->
                    <Grid Grid.Column="1"
                          x:Name="PropertyPanelContent">
                        <Grid.Style>
                            <Style TargetType="Grid">
                                <Setter Property="Width"
                                        Value="450"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsChecked, ElementName=PropertyPanelToggle}"
                                                 Value="False">
                                        <Setter Property="Width"
                                                Value="0"/>
                                        <Setter Property="Visibility"
                                                Value="Collapsed"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Grid.Style>

                        <ContentControl x:Name="PropertyPanelContainer"/>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </DockPanel>
</Window>

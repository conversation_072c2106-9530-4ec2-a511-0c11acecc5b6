using System;
using System.Collections.Generic;
using ProjectDigitizer.Studio.Interfaces;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Functions
{
    /// <summary>
    /// 绝对值函数
    /// </summary>
    public class AbsFunction : FunctionProviderBase
    {
        public override string Name => "ABS";
        public override string DisplayName => "绝对值";
        public override string Category => "数学";
        public override string Description => "计算数值的绝对值";
        public override FunctionType Type => FunctionType.Math;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "value",
                    DisplayName = "数值",
                    DataType = FieldDataType.Number,
                    IsRequired = true,
                    Description = "要计算绝对值的数值"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var value = GetParameterValue<double>(parameters, "value", 0);
                var result = Math.Abs(value);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"绝对值计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "ABS(-5) = 5",
                "ABS(3.14) = 3.14",
                "ABS({field})"
            };
        }
    }

    /// <summary>
    /// 四舍五入函数
    /// </summary>
    public class RoundFunction : FunctionProviderBase
    {
        public override string Name => "ROUND";
        public override string DisplayName => "四舍五入";
        public override string Category => "数学";
        public override string Description => "将数值四舍五入到指定的小数位数";
        public override FunctionType Type => FunctionType.Math;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "value",
                    DisplayName = "数值",
                    DataType = FieldDataType.Number,
                    IsRequired = true,
                    Description = "要四舍五入的数值"
                },
                new ParameterDefinition
                {
                    Name = "digits",
                    DisplayName = "小数位数",
                    DataType = FieldDataType.Number,
                    IsRequired = false,
                    DefaultValue = 0,
                    Description = "保留的小数位数，默认为0"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var value = GetParameterValue<double>(parameters, "value", 0);
                var digits = GetParameterValue<int>(parameters, "digits", 0);
                
                var result = Math.Round(value, digits);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"四舍五入计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "ROUND(3.14159) = 3",
                "ROUND(3.14159, 2) = 3.14",
                "ROUND({field}, 1)"
            };
        }
    }

    /// <summary>
    /// 幂函数
    /// </summary>
    public class PowerFunction : FunctionProviderBase
    {
        public override string Name => "POWER";
        public override string DisplayName => "幂运算";
        public override string Category => "数学";
        public override string Description => "计算数值的幂";
        public override FunctionType Type => FunctionType.Math;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "base",
                    DisplayName = "底数",
                    DataType = FieldDataType.Number,
                    IsRequired = true,
                    Description = "底数"
                },
                new ParameterDefinition
                {
                    Name = "exponent",
                    DisplayName = "指数",
                    DataType = FieldDataType.Number,
                    IsRequired = true,
                    Description = "指数"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var baseValue = GetParameterValue<double>(parameters, "base", 0);
                var exponent = GetParameterValue<double>(parameters, "exponent", 0);
                
                var result = Math.Pow(baseValue, exponent);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"幂运算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "POWER(2, 3) = 8",
                "POWER(10, 2) = 100",
                "POWER({base}, {exponent})"
            };
        }
    }

    /// <summary>
    /// 平方根函数
    /// </summary>
    public class SqrtFunction : FunctionProviderBase
    {
        public override string Name => "SQRT";
        public override string DisplayName => "平方根";
        public override string Category => "数学";
        public override string Description => "计算数值的平方根";
        public override FunctionType Type => FunctionType.Math;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "value",
                    DisplayName = "数值",
                    DataType = FieldDataType.Number,
                    IsRequired = true,
                    MinValue = 0,
                    Description = "要计算平方根的数值（必须非负）"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var value = GetParameterValue<double>(parameters, "value", 0);
                
                if (value < 0)
                {
                    return FunctionResult.Error("不能计算负数的平方根");
                }
                
                var result = Math.Sqrt(value);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"平方根计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "SQRT(9) = 3",
                "SQRT(16) = 4",
                "SQRT({field})"
            };
        }
    }
}

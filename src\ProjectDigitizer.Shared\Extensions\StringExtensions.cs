namespace ProjectDigitizer.Shared.Extensions;

/// <summary>
/// 字符串扩展方法
/// </summary>
public static class StringExtensions
{
    /// <summary>
    /// 检查字符串是否为空或空白
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>是否为空或空白</returns>
    public static bool IsNullOrWhiteSpace(this string? value)
    {
        return string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    /// 检查字符串是否不为空且不为空白
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>是否不为空且不为空白</returns>
    public static bool IsNotNullOrWhiteSpace(this string? value)
    {
        return !string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    /// 安全的Trim操作
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>Trim后的字符串，如果原字符串为null则返回空字符串</returns>
    public static string SafeTrim(this string? value)
    {
        return value?.Trim() ?? string.Empty;
    }

    /// <summary>
    /// 截断字符串到指定长度
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="suffix">超长时的后缀</param>
    /// <returns>截断后的字符串</returns>
    public static string Truncate(this string? value, int maxLength, string suffix = "...")
    {
        if (string.IsNullOrEmpty(value) || value.Length <= maxLength)
            return value ?? string.Empty;

        return value.Substring(0, maxLength - suffix.Length) + suffix;
    }

    /// <summary>
    /// 转换为Pascal命名法
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>Pascal命名法字符串</returns>
    public static string ToPascalCase(this string? value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return string.Empty;

        var words = value.Split(new[] { ' ', '_', '-' }, StringSplitOptions.RemoveEmptyEntries);
        return string.Concat(words.Select(word => 
            char.ToUpperInvariant(word[0]) + word.Substring(1).ToLowerInvariant()));
    }

    /// <summary>
    /// 转换为Camel命名法
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>Camel命名法字符串</returns>
    public static string ToCamelCase(this string? value)
    {
        var pascalCase = value.ToPascalCase();
        if (string.IsNullOrEmpty(pascalCase))
            return string.Empty;

        return char.ToLowerInvariant(pascalCase[0]) + pascalCase.Substring(1);
    }
}

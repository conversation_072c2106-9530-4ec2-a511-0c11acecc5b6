using Microsoft.Extensions.Logging;
using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.Models;
using ProjectDigitizer.Core.Enums;

namespace ProjectDigitizer.Application.Services;

/// <summary>
/// 模块服务实现
/// </summary>
public class ModuleService : IModuleService
{
    private readonly ILogger<ModuleService> _logger;
    private readonly Dictionary<string, ModuleModel> _modules;

    public ModuleService(ILogger<ModuleService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _modules = new Dictionary<string, ModuleModel>();
    }

    /// <summary>
    /// 创建模块
    /// </summary>
    public ModuleModel CreateModule(ModuleType moduleType, string? name = null)
    {
        try
        {
            var module = new ModuleModel
            {
                Type = moduleType,
                Name = name ?? GetDefaultModuleName(moduleType),
                NodeType = GetNodeTypeFromModuleType(moduleType)
            };

            _modules[module.Id] = module;
            
            _logger.LogInformation("创建模块成功: {ModuleId}, 类型: {ModuleType}", module.Id, moduleType);
            
            return module;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建模块失败: {ModuleType}", moduleType);
            throw;
        }
    }

    /// <summary>
    /// 获取模块
    /// </summary>
    public ModuleModel? GetModule(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
            return null;

        _modules.TryGetValue(id, out var module);
        return module;
    }

    /// <summary>
    /// 更新模块
    /// </summary>
    public bool UpdateModule(ModuleModel module)
    {
        if (module == null)
            return false;

        try
        {
            _modules[module.Id] = module;
            _logger.LogInformation("更新模块成功: {ModuleId}", module.Id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新模块失败: {ModuleId}", module.Id);
            return false;
        }
    }

    /// <summary>
    /// 删除模块
    /// </summary>
    public bool DeleteModule(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
            return false;

        try
        {
            var removed = _modules.Remove(id);
            if (removed)
            {
                _logger.LogInformation("删除模块成功: {ModuleId}", id);
            }
            return removed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除模块失败: {ModuleId}", id);
            return false;
        }
    }

    /// <summary>
    /// 获取所有模块
    /// </summary>
    public IEnumerable<ModuleModel> GetAllModules()
    {
        return _modules.Values.ToList();
    }

    /// <summary>
    /// 根据类型获取模块
    /// </summary>
    public IEnumerable<ModuleModel> GetModulesByType(ModuleType moduleType)
    {
        return _modules.Values.Where(m => m.Type == moduleType).ToList();
    }

    /// <summary>
    /// 验证模块配置
    /// </summary>
    public ValidationResult ValidateModule(ModuleModel module)
    {
        if (module == null)
            return ValidationResult.Failure("模块不能为空");

        var errors = new List<string>();
        var warnings = new List<string>();

        // 验证基本属性
        if (string.IsNullOrWhiteSpace(module.Name))
            errors.Add("模块名称不能为空");

        if (string.IsNullOrWhiteSpace(module.Id))
            errors.Add("模块ID不能为空");

        // 根据模块类型进行特定验证
        ValidateModuleTypeSpecific(module, errors, warnings);

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            Errors = errors,
            Warnings = warnings
        };
    }

    #region Private Methods

    private string GetDefaultModuleName(ModuleType moduleType)
    {
        return moduleType switch
        {
            ModuleType.PipeLine => "平面管线",
            ModuleType.RiserPipe => "立管",
            ModuleType.DataFilter => "数据过滤",
            ModuleType.ExcelExport => "Excel导出",
            _ => moduleType.ToString()
        };
    }

    private NodeType GetNodeTypeFromModuleType(ModuleType moduleType)
    {
        return moduleType switch
        {
            ModuleType.FileInput or ModuleType.DatabaseInput or ModuleType.APIInput or 
            ModuleType.CADInput or ModuleType.ExcelInput or ModuleType.CSVInput or 
            ModuleType.XMLInput or ModuleType.JSONInput or ModuleType.ManualDataInput => NodeType.Input,
            
            ModuleType.FileGeneration or ModuleType.CADExport or ModuleType.ExcelExport or 
            ModuleType.CSVExport or ModuleType.WordExport or ModuleType.NotificationAlert => NodeType.Output,
            
            ModuleType.ConditionalBranch or ModuleType.LoopProcessor or ModuleType.ErrorHandler or 
            ModuleType.FlowControl => NodeType.Control,
            
            _ => NodeType.Process
        };
    }

    private void ValidateModuleTypeSpecific(ModuleModel module, List<string> errors, List<string> warnings)
    {
        // 根据不同模块类型进行特定验证
        switch (module.Type)
        {
            case ModuleType.DataFilter:
                ValidateDataFilterModule(module, errors, warnings);
                break;
            case ModuleType.ExcelExport:
                ValidateExcelExportModule(module, errors, warnings);
                break;
            // 可以继续添加其他模块类型的验证
        }
    }

    private void ValidateDataFilterModule(ModuleModel module, List<string> errors, List<string> warnings)
    {
        if (!module.Parameters.ContainsKey("filterCondition"))
            errors.Add("数据过滤模块必须设置筛选条件");
    }

    private void ValidateExcelExportModule(ModuleModel module, List<string> errors, List<string> warnings)
    {
        if (!module.Parameters.ContainsKey("filePath"))
            warnings.Add("建议设置Excel导出文件路径");
    }

    #endregion
}

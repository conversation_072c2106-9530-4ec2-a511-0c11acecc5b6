using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace ProjectDigitizer.Studio.Models
{
    /// <summary>
    /// 筛选表达式模型
    /// </summary>
    public class FilterExpression
    {
        /// <summary>
        /// 原始表达式字符串
        /// </summary>
        public string RawExpression { get; set; } = string.Empty;

        /// <summary>
        /// 解析后的表达式树
        /// </summary>
        public ExpressionNode? RootNode { get; set; }

        /// <summary>
        /// 表达式中使用的字段列表
        /// </summary>
        public List<string> UsedFields { get; set; } = new();

        /// <summary>
        /// 是否区分大小写
        /// </summary>
        public bool CaseSensitive { get; set; } = false;

        /// <summary>
        /// 表达式是否有效
        /// </summary>
        public bool IsValid { get; set; } = false;

        /// <summary>
        /// 验证错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 表达式节点基类
    /// </summary>
    public abstract class ExpressionNode
    {
        /// <summary>
        /// 节点类型
        /// </summary>
        public abstract ExpressionNodeType NodeType { get; }

        /// <summary>
        /// 计算表达式值
        /// </summary>
        /// <param name="context">数据上下文</param>
        /// <returns>计算结果</returns>
        public abstract object? Evaluate(Dictionary<string, object?> context);

        /// <summary>
        /// 获取表达式字符串表示
        /// </summary>
        /// <returns>表达式字符串</returns>
        public abstract new string ToString();
    }

    /// <summary>
    /// 表达式节点类型
    /// </summary>
    public enum ExpressionNodeType
    {
        /// <summary>
        /// 常量值
        /// </summary>
        Constant,

        /// <summary>
        /// 字段引用
        /// </summary>
        Field,

        /// <summary>
        /// 二元运算符
        /// </summary>
        BinaryOperator,

        /// <summary>
        /// 一元运算符
        /// </summary>
        UnaryOperator,

        /// <summary>
        /// 函数调用
        /// </summary>
        FunctionCall
    }

    /// <summary>
    /// 常量节点
    /// </summary>
    public class ConstantNode : ExpressionNode
    {
        public object? Value { get; set; }

        public override ExpressionNodeType NodeType => ExpressionNodeType.Constant;

        public ConstantNode(object? value)
        {
            Value = value;
        }

        public override object? Evaluate(Dictionary<string, object?> context)
        {
            return Value;
        }

        public override string ToString()
        {
            return Value switch
            {
                string s => $"\"{s}\"",
                null => "null",
                _ => Value.ToString() ?? "null"
            };
        }
    }

    /// <summary>
    /// 字段引用节点
    /// </summary>
    public class FieldNode : ExpressionNode
    {
        public string FieldName { get; set; }

        public override ExpressionNodeType NodeType => ExpressionNodeType.Field;

        public FieldNode(string fieldName)
        {
            FieldName = fieldName;
        }

        public override object? Evaluate(Dictionary<string, object?> context)
        {
            return context.TryGetValue(FieldName, out var value) ? value : null;
        }

        public override string ToString()
        {
            return FieldName;
        }
    }

    /// <summary>
    /// 二元运算符节点
    /// </summary>
    public class BinaryOperatorNode : ExpressionNode
    {
        public ExpressionNode Left { get; set; }
        public ExpressionNode Right { get; set; }
        public BinaryOperatorType Operator { get; set; }

        public override ExpressionNodeType NodeType => ExpressionNodeType.BinaryOperator;

        public BinaryOperatorNode(ExpressionNode left, BinaryOperatorType op, ExpressionNode right)
        {
            Left = left;
            Operator = op;
            Right = right;
        }

        public override object? Evaluate(Dictionary<string, object?> context)
        {
            var leftValue = Left.Evaluate(context);
            var rightValue = Right.Evaluate(context);

            return Operator switch
            {
                BinaryOperatorType.Equal => CompareValues(leftValue, rightValue) == 0,
                BinaryOperatorType.NotEqual => CompareValues(leftValue, rightValue) != 0,
                BinaryOperatorType.GreaterThan => CompareValues(leftValue, rightValue) > 0,
                BinaryOperatorType.GreaterThanOrEqual => CompareValues(leftValue, rightValue) >= 0,
                BinaryOperatorType.LessThan => CompareValues(leftValue, rightValue) < 0,
                BinaryOperatorType.LessThanOrEqual => CompareValues(leftValue, rightValue) <= 0,
                BinaryOperatorType.And => ConvertToBool(leftValue) && ConvertToBool(rightValue),
                BinaryOperatorType.Or => ConvertToBool(leftValue) || ConvertToBool(rightValue),
                _ => throw new NotSupportedException($"不支持的运算符: {Operator}")
            };
        }

        public override string ToString()
        {
            var opStr = Operator switch
            {
                BinaryOperatorType.Equal => "==",
                BinaryOperatorType.NotEqual => "!=",
                BinaryOperatorType.GreaterThan => ">",
                BinaryOperatorType.GreaterThanOrEqual => ">=",
                BinaryOperatorType.LessThan => "<",
                BinaryOperatorType.LessThanOrEqual => "<=",
                BinaryOperatorType.And => "&&",
                BinaryOperatorType.Or => "||",
                _ => "?"
            };
            return $"({Left} {opStr} {Right})";
        }

        private static int CompareValues(object? left, object? right)
        {
            if (left == null && right == null) return 0;
            if (left == null) return -1;
            if (right == null) return 1;

            // 尝试数值比较
            if (TryConvertToDouble(left, out var leftNum) && TryConvertToDouble(right, out var rightNum))
            {
                return leftNum.CompareTo(rightNum);
            }

            // 字符串比较
            return string.Compare(left.ToString(), right.ToString(), StringComparison.OrdinalIgnoreCase);
        }

        private static bool TryConvertToDouble(object? value, out double result)
        {
            result = 0;
            return value != null && double.TryParse(value.ToString(), out result);
        }

        private static bool ConvertToBool(object? value)
        {
            return value switch
            {
                bool b => b,
                null => false,
                string s => !string.IsNullOrEmpty(s),
                int i => i != 0,
                double d => d != 0.0,
                _ => true
            };
        }
    }

    /// <summary>
    /// 一元运算符节点
    /// </summary>
    public class UnaryOperatorNode : ExpressionNode
    {
        public ExpressionNode Operand { get; set; }
        public UnaryOperatorType Operator { get; set; }

        public override ExpressionNodeType NodeType => ExpressionNodeType.UnaryOperator;

        public UnaryOperatorNode(UnaryOperatorType op, ExpressionNode operand)
        {
            Operator = op;
            Operand = operand;
        }

        public override object? Evaluate(Dictionary<string, object?> context)
        {
            var value = Operand.Evaluate(context);

            return Operator switch
            {
                UnaryOperatorType.Not => !ConvertToBool(value),
                _ => throw new NotSupportedException($"不支持的一元运算符: {Operator}")
            };
        }

        public override string ToString()
        {
            var opStr = Operator switch
            {
                UnaryOperatorType.Not => "!",
                _ => "?"
            };
            return $"{opStr}({Operand})";
        }

        private static bool ConvertToBool(object? value)
        {
            return value switch
            {
                bool b => b,
                null => false,
                string s => !string.IsNullOrEmpty(s),
                int i => i != 0,
                double d => d != 0.0,
                _ => true
            };
        }
    }

    /// <summary>
    /// 二元运算符类型
    /// </summary>
    public enum BinaryOperatorType
    {
        Equal,              // ==
        NotEqual,           // !=
        GreaterThan,        // >
        GreaterThanOrEqual, // >=
        LessThan,           // <
        LessThanOrEqual,    // <=
        And,                // &&
        Or                  // ||
    }

    /// <summary>
    /// 一元运算符类型
    /// </summary>
    public enum UnaryOperatorType
    {
        Not                 // !
    }

    /// <summary>
    /// 表达式解析结果
    /// </summary>
    public class ExpressionParseResult
    {
        /// <summary>
        /// 是否解析成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 解析后的表达式
        /// </summary>
        public FilterExpression? Expression { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 警告信息
        /// </summary>
        public List<string> Warnings { get; set; } = new();
    }

    /// <summary>
    /// 表达式验证结果
    /// </summary>
    public class ExpressionValidationResult
    {
        /// <summary>
        /// 是否验证通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;
    }
}

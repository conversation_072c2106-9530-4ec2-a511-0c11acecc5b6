using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Windows.Threading;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 性能监控器 - 监控WPF节点渲染性能
    /// </summary>
    public class PerformanceMonitor
    {
        private readonly DispatcherTimer _monitorTimer;
        private readonly Queue<PerformanceMetrics> _metricsHistory;
        private readonly Stopwatch _frameStopwatch;
        private int _frameCount;
        private DateTime _lastReportTime;
        private const int MAX_HISTORY_SIZE = 100;
        private const int REPORT_INTERVAL_MS = 1000; // 每秒报告一次

        public PerformanceMonitor()
        {
            _metricsHistory = new Queue<PerformanceMetrics>();
            _frameStopwatch = new Stopwatch();
            _lastReportTime = DateTime.Now;

            _monitorTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(REPORT_INTERVAL_MS)
            };
            _monitorTimer.Tick += OnMonitorTimerTick;
        }

        /// <summary>
        /// 当前性能指标
        /// </summary>
        public PerformanceMetrics CurrentMetrics { get; private set; } = new PerformanceMetrics();

        /// <summary>
        /// 性能指标历史记录
        /// </summary>
        public IEnumerable<PerformanceMetrics> MetricsHistory => _metricsHistory;

        /// <summary>
        /// 性能报告事件
        /// </summary>
        public event Action<PerformanceReport>? PerformanceReported;

        /// <summary>
        /// 开始监控
        /// </summary>
        public void StartMonitoring()
        {
            _frameStopwatch.Start();
            _monitorTimer.Start();
            _lastReportTime = DateTime.Now;
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring()
        {
            _frameStopwatch.Stop();
            _monitorTimer.Stop();
        }

        /// <summary>
        /// 记录帧开始
        /// </summary>
        public void BeginFrame()
        {
            _frameStopwatch.Restart();
        }

        /// <summary>
        /// 记录帧结束
        /// </summary>
        public void EndFrame()
        {
            _frameStopwatch.Stop();
            _frameCount++;

            CurrentMetrics.LastFrameTime = _frameStopwatch.ElapsedMilliseconds;
            CurrentMetrics.TotalFrames = _frameCount;
        }

        /// <summary>
        /// 记录节点渲染开始
        /// </summary>
        public void BeginNodeRender(int nodeCount)
        {
            CurrentMetrics.NodesRendered = nodeCount;
        }

        /// <summary>
        /// 记录连接器更新
        /// </summary>
        public void RecordConnectorUpdate(int connectorCount)
        {
            CurrentMetrics.ConnectorsUpdated = connectorCount;
        }

        /// <summary>
        /// 记录内存使用情况
        /// </summary>
        public void RecordMemoryUsage()
        {
            var process = Process.GetCurrentProcess();
            CurrentMetrics.MemoryUsageMB = process.WorkingSet64 / (1024 * 1024);
        }

        private void OnMonitorTimerTick(object? sender, EventArgs e)
        {
            var now = DateTime.Now;
            var elapsed = (now - _lastReportTime).TotalSeconds;

            // 计算FPS
            CurrentMetrics.FPS = _frameCount / elapsed;
            CurrentMetrics.Timestamp = now;

            // 记录内存使用
            RecordMemoryUsage();

            // 添加到历史记录
            _metricsHistory.Enqueue(CurrentMetrics);
            if (_metricsHistory.Count > MAX_HISTORY_SIZE)
            {
                _metricsHistory.Dequeue();
            }

            // 生成性能报告
            var report = GeneratePerformanceReport();
            PerformanceReported?.Invoke(report);

            // 重置计数器
            _frameCount = 0;
            _lastReportTime = now;
            CurrentMetrics = new PerformanceMetrics();
        }

        private PerformanceReport GeneratePerformanceReport()
        {
            var recent = _metricsHistory.TakeLast(10).ToList();

            return new PerformanceReport
            {
                AverageFPS = recent.Count > 0 ? recent.Average(m => m.FPS) : 0,
                AverageFrameTime = recent.Count > 0 ? recent.Average(m => m.LastFrameTime) : 0,
                MaxFrameTime = recent.Count > 0 ? recent.Max(m => m.LastFrameTime) : 0,
                CurrentMemoryMB = CurrentMetrics.MemoryUsageMB,
                NodesRendered = CurrentMetrics.NodesRendered,
                ConnectorsUpdated = CurrentMetrics.ConnectorsUpdated,
                Timestamp = DateTime.Now,
                PerformanceLevel = CalculatePerformanceLevel(recent)
            };
        }

        private PerformanceLevel CalculatePerformanceLevel(List<PerformanceMetrics> recent)
        {
            if (recent.Count == 0) return PerformanceLevel.Unknown;

            var avgFps = recent.Average(m => m.FPS);
            var maxFrameTime = recent.Max(m => m.LastFrameTime);

            if (avgFps >= 50 && maxFrameTime <= 20)
                return PerformanceLevel.Excellent;
            else if (avgFps >= 30 && maxFrameTime <= 33)
                return PerformanceLevel.Good;
            else if (avgFps >= 20 && maxFrameTime <= 50)
                return PerformanceLevel.Fair;
            else
                return PerformanceLevel.Poor;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            StopMonitoring();
            _metricsHistory.Clear();
        }
    }

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetrics
    {
        public DateTime Timestamp { get; set; }
        public double FPS { get; set; }
        public double LastFrameTime { get; set; }
        public int TotalFrames { get; set; }
        public long MemoryUsageMB { get; set; }
        public int NodesRendered { get; set; }
        public int ConnectorsUpdated { get; set; }
    }

    /// <summary>
    /// 性能报告
    /// </summary>
    public class PerformanceReport
    {
        public DateTime Timestamp { get; set; }
        public double AverageFPS { get; set; }
        public double AverageFrameTime { get; set; }
        public double MaxFrameTime { get; set; }
        public long CurrentMemoryMB { get; set; }
        public int NodesRendered { get; set; }
        public int ConnectorsUpdated { get; set; }
        public PerformanceLevel PerformanceLevel { get; set; }

        public override string ToString()
        {
            return $"FPS: {AverageFPS:F1}, 帧时间: {AverageFrameTime:F1}ms, " +
                   $"内存: {CurrentMemoryMB}MB, 节点: {NodesRendered}, " +
                   $"性能等级: {PerformanceLevel}";
        }
    }

    /// <summary>
    /// 性能等级
    /// </summary>
    public enum PerformanceLevel
    {
        Unknown,
        Poor,
        Fair,
        Good,
        Excellent
    }

    /// <summary>
    /// 性能监控静态管理器
    /// </summary>
    public static class PerformanceMonitorManager
    {
        private static readonly PerformanceMonitor _globalMonitor = new();

        /// <summary>
        /// 获取全局性能监控器
        /// </summary>
        public static PerformanceMonitor Instance => _globalMonitor;

        /// <summary>
        /// 启用性能监控
        /// </summary>
        public static void StartMonitoring()
        {
            _globalMonitor.StartMonitoring();

            // 监听性能报告
            _globalMonitor.PerformanceReported += report =>
            {
                Debug.WriteLine($"性能报告: {report}");

                // 如果性能较差，可以触发优化措施
                if (report.PerformanceLevel <= PerformanceLevel.Fair)
                {
                    Debug.WriteLine("检测到性能问题，建议启用更多优化措施");
                }
            };
        }

        /// <summary>
        /// 停止性能监控
        /// </summary>
        public static void StopMonitoring()
        {
            _globalMonitor.StopMonitoring();
        }
    }
}

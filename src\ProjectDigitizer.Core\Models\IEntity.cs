namespace ProjectDigitizer.Core.Models;

/// <summary>
/// 实体基础接口
/// </summary>
public interface IEntity
{
    /// <summary>
    /// 实体唯一标识符
    /// </summary>
    string Id { get; }
}

/// <summary>
/// 实体基础接口（泛型版本）
/// </summary>
/// <typeparam name="TKey">主键类型</typeparam>
public interface IEntity<TKey> : IEntity
{
    /// <summary>
    /// 实体唯一标识符（强类型）
    /// </summary>
    new TKey Id { get; }
}

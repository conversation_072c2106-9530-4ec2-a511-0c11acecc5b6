using ProjectDigitizer.Core.Enums;

namespace ProjectDigitizer.Core.Models;

/// <summary>
/// 模块模型 - 核心业务实体
/// </summary>
public class ModuleModel : EntityBase
{
    private string _name = "未命名模块";
    private string _description = "模块描述";
    private ModuleType _type = ModuleType.PipeLine;
    private NodeType _nodeType = NodeType.Input;
    private bool _isEnabled = true;
    private bool _isVisible = true;
    private List<string> _conditions = new();
    private Dictionary<string, object> _parameters = new();
    private string _codeTemplate = string.Empty;

    public ModuleModel() : base()
    {
    }

    public ModuleModel(string id) : base(id)
    {
    }

    /// <summary>
    /// 模块名称
    /// </summary>
    public string Name
    {
        get => _name;
        set => SetProperty(ref _name, value);
    }

    /// <summary>
    /// 模块描述
    /// </summary>
    public string Description
    {
        get => _description;
        set => SetProperty(ref _description, value);
    }

    /// <summary>
    /// 模块类型
    /// </summary>
    public ModuleType Type
    {
        get => _type;
        set => SetProperty(ref _type, value);
    }

    /// <summary>
    /// 节点类型
    /// </summary>
    public NodeType NodeType
    {
        get => _nodeType;
        set => SetProperty(ref _nodeType, value);
    }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled
    {
        get => _isEnabled;
        set => SetProperty(ref _isEnabled, value);
    }

    /// <summary>
    /// 是否可见
    /// </summary>
    public bool IsVisible
    {
        get => _isVisible;
        set => SetProperty(ref _isVisible, value);
    }

    /// <summary>
    /// 条件列表
    /// </summary>
    public List<string> Conditions
    {
        get => _conditions;
        set => SetProperty(ref _conditions, value);
    }

    /// <summary>
    /// 参数字典
    /// </summary>
    public Dictionary<string, object> Parameters
    {
        get => _parameters;
        set => SetProperty(ref _parameters, value);
    }

    /// <summary>
    /// 代码模板（用于代码生成）
    /// </summary>
    public string CodeTemplate
    {
        get => _codeTemplate;
        set => SetProperty(ref _codeTemplate, value);
    }

    /// <summary>
    /// 获取参数值
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="key">参数键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>参数值</returns>
    public T GetParameter<T>(string key, T defaultValue = default!)
    {
        if (Parameters.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return defaultValue;
    }

    /// <summary>
    /// 设置参数值
    /// </summary>
    /// <param name="key">参数键</param>
    /// <param name="value">参数值</param>
    public void SetParameter(string key, object value)
    {
        Parameters[key] = value;
        OnPropertyChanged(nameof(Parameters));
    }

    /// <summary>
    /// 移除参数
    /// </summary>
    /// <param name="key">参数键</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveParameter(string key)
    {
        var removed = Parameters.Remove(key);
        if (removed)
        {
            OnPropertyChanged(nameof(Parameters));
        }
        return removed;
    }

    /// <summary>
    /// 检查是否包含参数
    /// </summary>
    /// <param name="key">参数键</param>
    /// <returns>是否包含</returns>
    public bool HasParameter(string key)
    {
        return Parameters.ContainsKey(key);
    }

    /// <summary>
    /// 获取所有参数键
    /// </summary>
    /// <returns>参数键集合</returns>
    public IEnumerable<string> GetParameterKeys()
    {
        return Parameters.Keys;
    }

    /// <summary>
    /// 清空所有参数
    /// </summary>
    public void ClearParameters()
    {
        Parameters.Clear();
        OnPropertyChanged(nameof(Parameters));
    }

    /// <summary>
    /// 复制参数到另一个模块
    /// </summary>
    /// <param name="targetModule">目标模块</param>
    public void CopyParametersTo(ModuleModel targetModule)
    {
        if (targetModule == null) return;

        foreach (var parameter in Parameters)
        {
            targetModule.SetParameter(parameter.Key, parameter.Value);
        }
    }

    /// <summary>
    /// 克隆模块
    /// </summary>
    /// <returns>克隆的模块</returns>
    public ModuleModel Clone()
    {
        var cloned = new ModuleModel
        {
            Name = Name + "_Copy",
            Description = Description,
            Type = Type,
            NodeType = NodeType,
            IsEnabled = IsEnabled,
            IsVisible = IsVisible,
            CodeTemplate = CodeTemplate,
            Conditions = new List<string>(Conditions),
            Parameters = new Dictionary<string, object>(Parameters)
        };

        return cloned;
    }
}

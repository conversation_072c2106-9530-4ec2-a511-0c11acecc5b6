using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 现代化属性面板 - Material Design 风格
    /// </summary>
    public partial class ModernPropertyPanel : UserControl
    {
        private ModuleNodeViewModel? _currentNode;
        private NodePropertyValues? _currentValues;

        /// <summary>
        /// 属性值变化事件
        /// </summary>
        public event EventHandler<PropertyValueChangedEventArgs>? PropertyValueChanged;

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        public event EventHandler? ApplyClicked;

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        public event EventHandler? ResetClicked;

        public ModernPropertyPanel()
        {
            InitializeComponent();
            InitializeEventHandlers();
        }

        /// <summary>
        /// 初始化事件处理器
        /// </summary>
        private void InitializeEventHandlers()
        {
            // 监听控件值变化
            FilterConditionTextBox.TextChanged += (s, e) => OnPropertyChanged("filterCondition", FilterConditionTextBox.Text);
            FilterTypeComboBox.SelectionChanged += (s, e) => OnPropertyChanged("filterType", GetSelectedValue(FilterTypeComboBox));
            CaseSensitiveCheckBox.Checked += (s, e) => OnPropertyChanged("caseSensitive", true);
            CaseSensitiveCheckBox.Unchecked += (s, e) => OnPropertyChanged("caseSensitive", false);
            AggregationEnabledCheckBox.Checked += (s, e) => OnPropertyChanged("aggregationEnabled", true);
            AggregationEnabledCheckBox.Unchecked += (s, e) => OnPropertyChanged("aggregationEnabled", false);
            AggregationFunctionComboBox.SelectionChanged += (s, e) => OnPropertyChanged("aggregationFunction", GetSelectedValue(AggregationFunctionComboBox));
            AggregationFieldTextBox.TextChanged += (s, e) => OnPropertyChanged("aggregationField", AggregationFieldTextBox.Text);
        }

        /// <summary>
        /// 设置当前节点
        /// </summary>
        /// <param name="node">节点视图模型</param>
        public void SetNode(ModuleNodeViewModel node)
        {
            _currentNode = node;
            _currentValues = node.PropertyValues ?? new NodePropertyValues();
            LoadPropertyValues();
        }

        /// <summary>
        /// 加载属性值到控件
        /// </summary>
        private void LoadPropertyValues()
        {
            if (_currentValues == null) return;

            try
            {
                // 基础设置
                FilterConditionTextBox.Text = _currentValues.GetValue("filterCondition")?.ToString() ?? "";
                SetComboBoxValue(FilterTypeComboBox, _currentValues.GetValue("filterType")?.ToString() ?? "include");

                // 筛选选项
                CaseSensitiveCheckBox.IsChecked = _currentValues.GetValue("caseSensitive") as bool? ?? false;

                // 统计功能
                var aggregationEnabled = _currentValues.GetValue("aggregationEnabled") as bool? ?? false;
                AggregationEnabledCheckBox.IsChecked = aggregationEnabled;
                UpdateStatisticsPanelVisibility(aggregationEnabled);

                SetComboBoxValue(AggregationFunctionComboBox, _currentValues.GetValue("aggregationFunction")?.ToString() ?? "count");
                AggregationFieldTextBox.Text = _currentValues.GetValue("aggregationField")?.ToString() ?? "";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载属性值失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 统计功能启用状态变化
        /// </summary>
        private void OnAggregationEnabledChanged(object sender, RoutedEventArgs e)
        {
            var isEnabled = AggregationEnabledCheckBox.IsChecked == true;
            UpdateStatisticsPanelVisibility(isEnabled);
        }

        /// <summary>
        /// 更新统计面板可见性
        /// </summary>
        private void UpdateStatisticsPanelVisibility(bool isVisible)
        {
            StatisticsPanel.Visibility = isVisible ? Visibility.Visible : Visibility.Collapsed;
        }

        /// <summary>
        /// 应用按钮点击
        /// </summary>
        private void OnApplyClick(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentNode != null && _currentValues != null)
                {
                    // 保存当前值到节点
                    var allValues = _currentValues.GetAllValues();
                    foreach (var kvp in allValues)
                    {
                        _currentNode.PropertyValues.SetValue(kvp.Key, kvp.Value);
                    }

                    // 触发应用事件
                    ApplyClicked?.Invoke(this, EventArgs.Empty);

                    // 显示成功提示
                    ShowNotification("配置已应用", NotificationType.Success);
                }
            }
            catch (Exception ex)
            {
                ShowNotification($"应用配置失败: {ex.Message}", NotificationType.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击
        /// </summary>
        private void OnResetClick(object sender, RoutedEventArgs e)
        {
            try
            {
                // 重置为默认值
                ResetToDefaults();

                // 触发重置事件
                ResetClicked?.Invoke(this, EventArgs.Empty);

                // 显示提示
                ShowNotification("配置已重置", NotificationType.Info);
            }
            catch (Exception ex)
            {
                ShowNotification($"重置配置失败: {ex.Message}", NotificationType.Error);
            }
        }

        /// <summary>
        /// 重置为默认值
        /// </summary>
        private void ResetToDefaults()
        {
            FilterConditionTextBox.Text = "";
            FilterTypeComboBox.SelectedIndex = 0; // include
            CaseSensitiveCheckBox.IsChecked = false;
            AggregationEnabledCheckBox.IsChecked = false;
            AggregationFunctionComboBox.SelectedIndex = 0; // count
            AggregationFieldTextBox.Text = "";

            UpdateStatisticsPanelVisibility(false);
        }

        /// <summary>
        /// 属性值变化处理
        /// </summary>
        private void OnPropertyChanged(string propertyName, object? value)
        {
            try
            {
                // 获取旧值
                var oldValue = _currentValues?.GetValue(propertyName);

                // 更新内部值存储
                _currentValues?.SetValue(propertyName, value);

                // 触发属性变化事件
                PropertyValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(propertyName, oldValue, value));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"属性变化处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取 ComboBox 选中值
        /// </summary>
        private string GetSelectedValue(ComboBox comboBox)
        {
            if (comboBox.SelectedItem is ComboBoxItem item)
            {
                return item.Tag?.ToString() ?? item.Content?.ToString() ?? "";
            }
            return "";
        }

        /// <summary>
        /// 设置 ComboBox 选中值
        /// </summary>
        private void SetComboBoxValue(ComboBox comboBox, string value)
        {
            foreach (ComboBoxItem item in comboBox.Items)
            {
                if (item.Tag?.ToString() == value)
                {
                    comboBox.SelectedItem = item;
                    break;
                }
            }
        }

        /// <summary>
        /// 显示通知
        /// </summary>
        private void ShowNotification(string message, NotificationType type)
        {
            // 这里可以集成到现有的通知系统
            System.Diagnostics.Debug.WriteLine($"[{type}] {message}");

            // 临时使用 MessageBox，后续可以替换为更好的通知组件
            var icon = type switch
            {
                NotificationType.Success => MessageBoxImage.Information,
                NotificationType.Error => MessageBoxImage.Error,
                NotificationType.Warning => MessageBoxImage.Warning,
                _ => MessageBoxImage.Information
            };

            MessageBox.Show(message, "属性配置", MessageBoxButton.OK, icon);
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        public Dictionary<string, object?> GetCurrentConfiguration()
        {
            return new Dictionary<string, object?>
            {
                ["filterCondition"] = FilterConditionTextBox.Text,
                ["filterType"] = GetSelectedValue(FilterTypeComboBox),
                ["caseSensitive"] = CaseSensitiveCheckBox.IsChecked == true,
                ["aggregationEnabled"] = AggregationEnabledCheckBox.IsChecked == true,
                ["aggregationFunction"] = GetSelectedValue(AggregationFunctionComboBox),
                ["aggregationField"] = AggregationFieldTextBox.Text
            };
        }

        /// <summary>
        /// 验证当前配置
        /// </summary>
        public Models.ValidationResult ValidateConfiguration()
        {
            var result = new Models.ValidationResult();

            // 验证筛选条件
            if (string.IsNullOrWhiteSpace(FilterConditionTextBox.Text))
            {
                result.AddError("筛选条件不能为空");
            }

            // 验证统计配置
            if (AggregationEnabledCheckBox.IsChecked == true)
            {
                var function = GetSelectedValue(AggregationFunctionComboBox);
                if (function != "count" && string.IsNullOrWhiteSpace(AggregationFieldTextBox.Text))
                {
                    result.AddError("统计函数需要指定字段名称");
                }
            }

            return result;
        }
    }



    /// <summary>
    /// 通知类型
    /// </summary>
    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error
    }
}

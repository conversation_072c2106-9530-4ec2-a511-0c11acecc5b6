using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProjectDigitizer.Application.ViewModels;

/// <summary>
/// ViewModel基类
/// </summary>
public abstract class ViewModelBase : INotifyPropertyChanged
{
    private bool _isBusy;
    private string _busyMessage = string.Empty;

    /// <summary>
    /// 是否忙碌状态
    /// </summary>
    public bool IsBusy
    {
        get => _isBusy;
        set => SetProperty(ref _isBusy, value);
    }

    /// <summary>
    /// 忙碌状态消息
    /// </summary>
    public string BusyMessage
    {
        get => _busyMessage;
        set => SetProperty(ref _busyMessage, value);
    }

    #region INotifyPropertyChanged Implementation

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    #endregion

    #region Batch Updates

    private bool _isInBatchUpdate;
    private readonly List<string> _pendingPropertyChanges = new();

    /// <summary>
    /// 开始批量更新
    /// </summary>
    protected void BeginBatchUpdate()
    {
        _isInBatchUpdate = true;
        _pendingPropertyChanges.Clear();
    }

    /// <summary>
    /// 结束批量更新并触发所有挂起的属性变更通知
    /// </summary>
    protected void EndBatchUpdate()
    {
        if (!_isInBatchUpdate) return;

        _isInBatchUpdate = false;

        foreach (var propertyName in _pendingPropertyChanges.Distinct())
        {
            OnPropertyChanged(propertyName);
        }

        _pendingPropertyChanges.Clear();
    }

    /// <summary>
    /// 执行批量更新
    /// </summary>
    /// <param name="updateAction">更新操作</param>
    protected void BatchUpdate(Action updateAction)
    {
        BeginBatchUpdate();
        try
        {
            updateAction?.Invoke();
        }
        finally
        {
            EndBatchUpdate();
        }
    }

    /// <summary>
    /// 批量更新模式下的属性变更通知
    /// </summary>
    protected override void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        if (_isInBatchUpdate && !string.IsNullOrEmpty(propertyName))
        {
            _pendingPropertyChanges.Add(propertyName);
        }
        else
        {
            base.OnPropertyChanged(propertyName);
        }
    }

    #endregion

    #region Async Operations

    /// <summary>
    /// 执行异步操作并处理忙碌状态
    /// </summary>
    /// <param name="operation">异步操作</param>
    /// <param name="busyMessage">忙碌消息</param>
    protected async Task ExecuteAsync(Func<Task> operation, string busyMessage = "处理中...")
    {
        if (IsBusy) return;

        try
        {
            IsBusy = true;
            BusyMessage = busyMessage;
            await operation();
        }
        finally
        {
            IsBusy = false;
            BusyMessage = string.Empty;
        }
    }

    /// <summary>
    /// 执行异步操作并处理忙碌状态（带返回值）
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="operation">异步操作</param>
    /// <param name="busyMessage">忙碌消息</param>
    /// <returns>操作结果</returns>
    protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string busyMessage = "处理中...")
    {
        if (IsBusy) return default;

        try
        {
            IsBusy = true;
            BusyMessage = busyMessage;
            return await operation();
        }
        finally
        {
            IsBusy = false;
            BusyMessage = string.Empty;
        }
    }

    #endregion

    #region Disposal

    private bool _disposed;

    /// <summary>
    /// 释放资源
    /// </summary>
    public virtual void Dispose()
    {
        if (_disposed) return;

        OnDisposing();
        _disposed = true;
    }

    /// <summary>
    /// 释放资源时调用
    /// </summary>
    protected virtual void OnDisposing()
    {
        // 子类可以重写此方法来释放特定资源
    }

    #endregion
}

using System.Text.Json;
using Microsoft.Extensions.Logging;
using ProjectDigitizer.Core.Interfaces;
using ProjectDigitizer.Core.Models;

namespace ProjectDigitizer.Infrastructure.Services;

/// <summary>
/// 项目文件管理服务实现
/// </summary>
public class ProjectFileService : IProjectFileService
{
    private readonly ILogger<ProjectFileService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public ProjectFileService(ILogger<ProjectFileService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
        };
    }

    /// <summary>
    /// 保存项目文件
    /// </summary>
    public async Task SaveProjectAsync(ProjectFile projectFile, string filePath)
    {
        try
        {
            if (projectFile == null)
                throw new ArgumentNullException(nameof(projectFile));

            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            // 更新最后修改时间
            projectFile.LastModifiedTime = DateTime.Now;

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 序列化为JSON
            var json = JsonSerializer.Serialize(projectFile, _jsonOptions);

            // 写入文件
            await File.WriteAllTextAsync(filePath, json);

            _logger.LogInformation("项目已保存到: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存项目失败: {FilePath}", filePath);
            throw new InvalidOperationException($"保存项目失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 加载项目文件
    /// </summary>
    public async Task<ProjectFile> LoadProjectAsync(string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"项目文件不存在: {filePath}");

            // 读取文件内容
            var json = await File.ReadAllTextAsync(filePath);

            // 反序列化
            var projectFile = JsonSerializer.Deserialize<ProjectFile>(json, _jsonOptions);

            if (projectFile == null)
                throw new InvalidOperationException("项目文件格式无效");

            _logger.LogInformation("项目已加载: {FilePath}", filePath);
            return projectFile;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载项目失败: {FilePath}", filePath);
            throw new InvalidOperationException($"加载项目失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 创建项目文件从画布数据
    /// </summary>
    public ProjectFile CreateProjectFileFromCanvas(CanvasData canvasData, ProjectInfo projectInfo)
    {
        try
        {
            var projectFile = new ProjectFile
            {
                ProjectInfo = projectInfo ?? new ProjectInfo(),
                CanvasData = canvasData ?? new CanvasData(),
                Version = "1.0",
                CreatedTime = DateTime.Now,
                LastModifiedTime = DateTime.Now
            };

            _logger.LogDebug("已创建项目文件，包含 {NodeCount} 个节点和 {ConnectionCount} 个连接", 
                canvasData?.Nodes?.Count ?? 0, canvasData?.Connections?.Count ?? 0);

            return projectFile;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建项目文件失败");
            throw;
        }
    }

    /// <summary>
    /// 保存模板文件
    /// </summary>
    public async Task SaveTemplateAsync(TemplateData templateData, string filePath)
    {
        try
        {
            if (templateData == null)
                throw new ArgumentNullException(nameof(templateData));

            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var json = JsonSerializer.Serialize(templateData, _jsonOptions);
            await File.WriteAllTextAsync(filePath, json);
            
            _logger.LogInformation("模板已保存到: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存模板失败: {FilePath}", filePath);
            throw new InvalidOperationException($"保存模板失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 加载模板文件
    /// </summary>
    public async Task<TemplateData> LoadTemplateAsync(string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"模板文件不存在: {filePath}");

            var json = await File.ReadAllTextAsync(filePath);
            var templateData = JsonSerializer.Deserialize<TemplateData>(json, _jsonOptions);

            if (templateData == null)
                throw new InvalidOperationException("模板文件格式无效");

            _logger.LogInformation("模板已加载: {FilePath}", filePath);
            return templateData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载模板失败: {FilePath}", filePath);
            throw new InvalidOperationException($"加载模板失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 验证项目文件
    /// </summary>
    public async Task<ValidationResult> ValidateProjectFileAsync(string filePath)
    {
        var result = new ValidationResult();

        try
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                result.AddError("文件路径不能为空");
                return result;
            }

            if (!File.Exists(filePath))
            {
                result.AddError($"文件不存在: {filePath}");
                return result;
            }

            // 尝试加载文件
            var projectFile = await LoadProjectAsync(filePath);

            // 验证项目信息
            if (projectFile.ProjectInfo == null)
            {
                result.AddError("项目信息缺失");
            }
            else
            {
                if (string.IsNullOrWhiteSpace(projectFile.ProjectInfo.Name))
                    result.AddWarning("项目名称为空");
            }

            // 验证画布数据
            if (projectFile.CanvasData == null)
            {
                result.AddError("画布数据缺失");
            }
            else
            {
                if (projectFile.CanvasData.Nodes?.Count == 0)
                    result.AddWarning("画布中没有节点");
            }

            _logger.LogDebug("项目文件验证完成: {FilePath}, 结果: {IsValid}", filePath, result.IsValid);
        }
        catch (Exception ex)
        {
            result.AddError($"验证过程中发生错误: {ex.Message}");
            _logger.LogError(ex, "验证项目文件失败: {FilePath}", filePath);
        }

        return result;
    }

    /// <summary>
    /// 获取项目文件信息
    /// </summary>
    public async Task<ProjectInfo?> GetProjectInfoAsync(string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                return null;

            var projectFile = await LoadProjectAsync(filePath);
            return projectFile.ProjectInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目信息失败: {FilePath}", filePath);
            return null;
        }
    }
}

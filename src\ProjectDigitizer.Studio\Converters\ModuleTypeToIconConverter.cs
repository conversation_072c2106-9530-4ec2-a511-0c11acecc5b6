using System;
using System.Globalization;
using System.Windows.Data;
using MaterialDesignThemes.Wpf;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 模块类型到图标的转换器
    /// </summary>
    public class ModuleTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value is ModuleType moduleType)
                {
                    return GetIconForModuleType(moduleType);
                }
            }
            catch (Exception)
            {
                // 如果转换失败，返回默认图标
            }
            return PackIconKind.Cog; // 默认图标
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 根据模块类型获取对应的图标
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <returns>图标类型</returns>
        private static PackIconKind GetIconForModuleType(ModuleType moduleType)
        {
            return moduleType switch
            {
                // 常规数据类 - 管道和基础设施图标
                ModuleType.PipeLine => PackIconKind.Pipe,
                ModuleType.RiserPipe => PackIconKind.PipeDisconnected,
                ModuleType.PressureBox => PackIconKind.Gauge,
                ModuleType.Excavation => PackIconKind.Shovel,
                ModuleType.Demolition => PackIconKind.Hammer,
                ModuleType.AntiCorrosion => PackIconKind.Shield,
                ModuleType.LightningProtection => PackIconKind.Flash,

                // 数据衍生关联类 - 连接和关联图标
                ModuleType.WarningBand => PackIconKind.Warning,
                ModuleType.WeldInspection => PackIconKind.Magnify,
                ModuleType.InstallationTeam => PackIconKind.AccountGroup,
                ModuleType.Measures => PackIconKind.Ruler,

                // 触发器类 - 触发和启动图标
                ModuleType.ClickTrigger => PackIconKind.CursorDefaultClick,
                ModuleType.AssociationTrigger => PackIconKind.Link,
                ModuleType.TimedTrigger => PackIconKind.Timer,
                ModuleType.FileChangeTrigger => PackIconKind.FileAlert,
                ModuleType.EnvironmentTrigger => PackIconKind.Earth,

                // 处理类 - 数据处理图标
                ModuleType.DataFilter => PackIconKind.Filter,
                ModuleType.TagSearch => PackIconKind.TagSearch,
                ModuleType.DataCalculation => PackIconKind.Calculator,
                ModuleType.DataValidation => PackIconKind.CheckCircle,
                ModuleType.DataTransform => PackIconKind.SwapHorizontal,
                ModuleType.DataCondition => PackIconKind.CodeBraces,
                ModuleType.DataMerge => PackIconKind.Merge,
                ModuleType.DataSort => PackIconKind.Sort,
                ModuleType.DataGroup => PackIconKind.Group,
                ModuleType.ArrayExpansion => PackIconKind.UnfoldMoreHorizontal,
                ModuleType.Other => PackIconKind.Cog,

                // 输入类 - 数据源图标
                ModuleType.FileInput => PackIconKind.File,
                ModuleType.DatabaseInput => PackIconKind.Database,
                ModuleType.APIInput => PackIconKind.Api,
                ModuleType.CADInput => PackIconKind.DrawingBox,
                ModuleType.ExcelInput => PackIconKind.FileExcel,
                ModuleType.CSVInput => PackIconKind.FileDelimited,
                ModuleType.XMLInput => PackIconKind.FileCode,
                ModuleType.JSONInput => PackIconKind.CodeJson,
                ModuleType.ManualDataInput => PackIconKind.Keyboard,

                // 智能体和控制类
                ModuleType.AIAgent => PackIconKind.Robot,
                ModuleType.ConditionalBranch => PackIconKind.SourceBranch,
                ModuleType.LoopProcessor => PackIconKind.Repeat,
                ModuleType.ErrorHandler => PackIconKind.AlertCircle,
                ModuleType.FlowControl => PackIconKind.TrafficLight,
                ModuleType.ScriptExecutor => PackIconKind.Script,
                ModuleType.VariableManager => PackIconKind.Variable,
                ModuleType.StateManager => PackIconKind.StateMachine,

                // 整理类 - 组织和管理图标
                ModuleType.TableManager => PackIconKind.Table,
                ModuleType.GraphicsAPI => PackIconKind.VectorTriangle,
                ModuleType.ExcelCSV => PackIconKind.FileExcel,
                ModuleType.WordProcessor => PackIconKind.FileWord,

                // 输出类 - 导出和输出图标
                ModuleType.FileGeneration => PackIconKind.FileDocument,
                ModuleType.ManualLocation => PackIconKind.MapMarker,
                ModuleType.SpecifiedPath => PackIconKind.FolderOpen,
                ModuleType.ThirdPartyAPI => PackIconKind.Api,
                ModuleType.CADExport => PackIconKind.FileOutline,
                ModuleType.ExcelExport => PackIconKind.FileExcel,
                ModuleType.CSVExport => PackIconKind.FileDelimited,
                ModuleType.WordExport => PackIconKind.FileWord,
                ModuleType.PPTExport => PackIconKind.FilePowerpoint,
                ModuleType.ImageExport => PackIconKind.FileImage,
                ModuleType.PublishRelease => PackIconKind.Publish,
                ModuleType.NotificationAlert => PackIconKind.Bell,
                ModuleType.DialogChat => PackIconKind.MessageText,
                ModuleType.OtherOutput => PackIconKind.Export,

                // 默认图标
                _ => PackIconKind.Cog
            };
        }
    }
}

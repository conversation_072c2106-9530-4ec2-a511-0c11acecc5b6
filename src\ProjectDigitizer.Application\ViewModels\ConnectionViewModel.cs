using System.Windows;

namespace ProjectDigitizer.Application.ViewModels;

/// <summary>
/// 连接线视图模型
/// </summary>
public class ConnectionViewModel : ViewModelBase
{
    private string _id = string.Empty;
    private ConnectorViewModel? _source;
    private ConnectorViewModel? _target;
    private ModuleNodeViewModel? _sourceNode;
    private ModuleNodeViewModel? _targetNode;
    private bool _isEnabled = true;
    private bool _isSelected;
    private string _strokeColor = "#2196F3";
    private double _strokeThickness = 2.0;
    private string _strokeDashArray = string.Empty;

    public ConnectionViewModel()
    {
        _id = Guid.NewGuid().ToString();
    }

    #region Properties

    /// <summary>
    /// 连接线唯一标识
    /// </summary>
    public string Id
    {
        get => _id;
        set => SetProperty(ref _id, value);
    }

    /// <summary>
    /// 源连接器
    /// </summary>
    public ConnectorViewModel? Source
    {
        get => _source;
        set
        {
            if (SetProperty(ref _source, value))
            {
                UpdateConnectionProperties();
            }
        }
    }

    /// <summary>
    /// 目标连接器
    /// </summary>
    public ConnectorViewModel? Target
    {
        get => _target;
        set
        {
            if (SetProperty(ref _target, value))
            {
                UpdateConnectionProperties();
            }
        }
    }

    /// <summary>
    /// 源节点
    /// </summary>
    public ModuleNodeViewModel? SourceNode
    {
        get => _sourceNode;
        set => SetProperty(ref _sourceNode, value);
    }

    /// <summary>
    /// 目标节点
    /// </summary>
    public ModuleNodeViewModel? TargetNode
    {
        get => _targetNode;
        set => SetProperty(ref _targetNode, value);
    }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled
    {
        get => _isEnabled;
        set
        {
            if (SetProperty(ref _isEnabled, value))
            {
                UpdateVisualState();
            }
        }
    }

    /// <summary>
    /// 是否选中
    /// </summary>
    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            if (SetProperty(ref _isSelected, value))
            {
                UpdateVisualState();
            }
        }
    }

    /// <summary>
    /// 连接线颜色
    /// </summary>
    public string StrokeColor
    {
        get => _strokeColor;
        set => SetProperty(ref _strokeColor, value);
    }

    /// <summary>
    /// 连接线粗细
    /// </summary>
    public double StrokeThickness
    {
        get => _strokeThickness;
        set => SetProperty(ref _strokeThickness, value);
    }

    /// <summary>
    /// 连接线虚线样式
    /// </summary>
    public string StrokeDashArray
    {
        get => _strokeDashArray;
        set => SetProperty(ref _strokeDashArray, value);
    }

    /// <summary>
    /// 连接线起点
    /// </summary>
    public Point SourcePoint => Source?.Anchor ?? new Point();

    /// <summary>
    /// 连接线终点
    /// </summary>
    public Point TargetPoint => Target?.Anchor ?? new Point();

    /// <summary>
    /// 连接是否有效
    /// </summary>
    public bool IsValid => Source != null && Target != null && Source.CanConnectTo(Target);

    /// <summary>
    /// 数据类型
    /// </summary>
    public string DataType => Source?.DataType ?? "Unknown";

    #endregion

    #region Public Methods

    /// <summary>
    /// 验证连接
    /// </summary>
    /// <returns>验证结果</returns>
    public bool ValidateConnection()
    {
        if (Source == null || Target == null)
            return false;

        if (Source == Target)
            return false;

        if (Source.Owner == Target.Owner)
            return false;

        if (Source.IsInput == Target.IsInput)
            return false;

        return Source.CanConnectTo(Target);
    }

    /// <summary>
    /// 断开连接
    /// </summary>
    public void Disconnect()
    {
        if (Source != null)
        {
            Source.UpdateConnectionStatus(false);
        }

        if (Target != null)
        {
            Target.UpdateConnectionStatus(false);
        }

        Source = null;
        Target = null;
        SourceNode = null;
        TargetNode = null;
    }

    /// <summary>
    /// 更新连接器状态
    /// </summary>
    public void UpdateConnectorStatus()
    {
        if (Source != null)
        {
            Source.UpdateConnectionStatus(true);
        }

        if (Target != null)
        {
            Target.UpdateConnectionStatus(true);
        }
    }

    #endregion

    #region Private Methods

    private void UpdateConnectionProperties()
    {
        // 更新源节点和目标节点
        SourceNode = Source?.Owner;
        TargetNode = Target?.Owner;

        // 更新连接器状态
        UpdateConnectorStatus();

        // 更新视觉状态
        UpdateVisualState();

        // 通知相关属性变化
        OnPropertyChanged(nameof(SourcePoint));
        OnPropertyChanged(nameof(TargetPoint));
        OnPropertyChanged(nameof(IsValid));
        OnPropertyChanged(nameof(DataType));
    }

    private void UpdateVisualState()
    {
        // 根据状态更新视觉属性
        if (!IsEnabled)
        {
            StrokeColor = "#808080"; // 灰色 - 禁用
            StrokeDashArray = "5,5"; // 虚线
            StrokeThickness = 1.0;
        }
        else if (IsSelected)
        {
            StrokeColor = "#FF9800"; // 橙色 - 选中
            StrokeDashArray = string.Empty; // 实线
            StrokeThickness = 3.0;
        }
        else
        {
            // 根据数据类型设置颜色
            StrokeColor = Source?.ConnectorColor ?? "#2196F3";
            StrokeDashArray = string.Empty; // 实线
            StrokeThickness = 2.0;
        }
    }

    #endregion

    #region Equality

    public override bool Equals(object? obj)
    {
        if (obj is ConnectionViewModel other)
        {
            return Id == other.Id;
        }
        return false;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    #endregion
}
